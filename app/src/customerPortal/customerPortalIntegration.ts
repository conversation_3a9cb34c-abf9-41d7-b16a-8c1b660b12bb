import { type Request, type Response } from 'express';
import { HttpError } from 'wasp/server';
import { z } from 'zod';
import crypto from 'crypto';

/**
 * Customer Portal Integration APIs
 * Provides subscription management interfaces for external websites
 */

// Validation schemas
const portalSessionSchema = z.object({
  userId: z.string().uuid().optional(),
  email: z.string().email().optional(),
  returnUrl: z.string().url(),
  features: z.array(z.enum([
    'subscription_management',
    'payment_methods',
    'billing_history',
    'usage_analytics',
    'account_settings',
    'support_tickets'
  ])).default(['subscription_management', 'payment_methods', 'billing_history']),
  theme: z.object({
    primaryColor: z.string().optional(),
    backgroundColor: z.string().optional(),
    fontFamily: z.string().optional(),
    logoUrl: z.string().url().optional(),
  }).optional(),
  sessionDuration: z.number().min(300).max(86400).default(3600), // 5 minutes to 24 hours
});

const paymentMethodUpdateSchema = z.object({
  userId: z.string().uuid(),
  paymentMethodId: z.string(),
  isDefault: z.boolean().default(false),
  billingAddress: z.object({
    line1: z.string(),
    line2: z.string().optional(),
    city: z.string(),
    state: z.string(),
    postalCode: z.string(),
    country: z.string(),
  }).optional(),
});

const billingPreferencesSchema = z.object({
  userId: z.string().uuid(),
  emailNotifications: z.boolean().default(true),
  invoiceDelivery: z.enum(['email', 'postal', 'both']).default('email'),
  currency: z.string().length(3).default('USD'),
  taxId: z.string().optional(),
  companyName: z.string().optional(),
});

interface PortalSession {
  sessionId: string;
  userId: string;
  portalUrl: string;
  features: string[];
  expiresAt: Date;
  createdAt: Date;
}

// In-memory storage for portal sessions (in production, use database)
const portalSessions = new Map<string, PortalSession>();

/**
 * Generate customer portal session URL
 */
export const handleGeneratePortalSession = async (req: Request, res: Response, context: any) => {
  try {
    // Validate request body
    const parsedBody = portalSessionSchema.safeParse(req.body);
    if (!parsedBody.success) {
      return res.status(400).json({
        success: false,
        message: 'Invalid portal session configuration',
        errors: parsedBody.error.format(),
      });
    }

    const { userId, email, returnUrl, features, theme, sessionDuration } = parsedBody.data;

    // Resolve user ID from email if not provided
    let resolvedUserId = userId;
    if (!resolvedUserId && email) {
      const user = await context.entities.User.findFirst({
        where: { email },
      });
      
      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found with provided email',
        });
      }
      
      resolvedUserId = user.id;
    }

    if (!resolvedUserId) {
      return res.status(400).json({
        success: false,
        message: 'Either userId or email must be provided',
      });
    }

    // Get user details for portal customization
    const user = await context.entities.User.findUnique({
      where: { id: resolvedUserId },
      include: {
        subscriptions: {
          include: {
            subscription: true,
          },
          where: {
            status: 'active',
          },
        },
      },
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found',
      });
    }

    // Generate secure session ID
    const sessionId = crypto.randomBytes(32).toString('hex');
    const expiresAt = new Date(Date.now() + sessionDuration * 1000);

    // Create portal session
    const portalSession: PortalSession = {
      sessionId,
      userId: resolvedUserId,
      portalUrl: generatePortalUrl(sessionId, features, theme),
      features,
      expiresAt,
      createdAt: new Date(),
    };

    // Store session
    portalSessions.set(sessionId, portalSession);

    // Get user subscription summary for portal context
    const subscriptionSummary = user.subscriptions.map((userSub: any) => ({
      id: userSub.id,
      name: userSub.subscription.name,
      status: userSub.status,
      price: userSub.subscription.price,
      priceFormatted: `$${(userSub.subscription.price / 100).toFixed(2)}`,
      interval: userSub.subscription.interval,
      nextBilling: userSub.endDate,
      creditsAllocated: userSub.creditsAllocated,
    }));

    return res.status(201).json({
      success: true,
      message: 'Customer portal session created successfully',
      data: {
        sessionId,
        portalUrl: portalSession.portalUrl,
        userId: resolvedUserId,
        userEmail: user.email,
        features,
        expiresAt,
        sessionDuration,
        returnUrl,
        userContext: {
          subscriptions: subscriptionSummary,
          totalCredits: user.credits,
          memberSince: user.createdAt,
        },
        theme,
        createdAt: portalSession.createdAt,
      },
    });
  } catch (error) {
    console.error('❌ Portal session generation failed:', error);
    
    if (error instanceof HttpError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message,
      });
    }

    return res.status(500).json({
      success: false,
      message: 'Internal server error during portal session generation',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

/**
 * Get customer portal session details
 */
export const handleGetPortalSession = async (req: Request, res: Response, context: any) => {
  try {
    const { sessionId } = req.params;

    if (!sessionId) {
      return res.status(400).json({
        success: false,
        message: 'Session ID is required',
      });
    }

    const portalSession = portalSessions.get(sessionId);
    if (!portalSession) {
      return res.status(404).json({
        success: false,
        message: 'Portal session not found or expired',
      });
    }

    // Check if session is expired
    if (new Date() > portalSession.expiresAt) {
      portalSessions.delete(sessionId);
      return res.status(410).json({
        success: false,
        message: 'Portal session has expired',
      });
    }

    // Get current user data
    const user = await context.entities.User.findUnique({
      where: { id: portalSession.userId },
      include: {
        subscriptions: {
          include: {
            subscription: true,
          },
        },
      },
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User associated with session not found',
      });
    }

    const sessionData = {
      sessionId: portalSession.sessionId,
      userId: portalSession.userId,
      features: portalSession.features,
      expiresAt: portalSession.expiresAt,
      createdAt: portalSession.createdAt,
      user: {
        id: user.id,
        email: user.email,
        credits: user.credits,
        subscriptions: user.subscriptions.map((userSub: any) => ({
          id: userSub.id,
          name: userSub.subscription.name,
          status: userSub.status,
          price: userSub.subscription.price,
          startDate: userSub.startDate,
          endDate: userSub.endDate,
          creditsAllocated: userSub.creditsAllocated,
        })),
      },
      timeRemaining: Math.max(0, Math.floor((portalSession.expiresAt.getTime() - Date.now()) / 1000)),
    };

    return res.status(200).json({
      success: true,
      message: 'Portal session details retrieved successfully',
      data: sessionData,
    });
  } catch (error) {
    console.error('❌ Failed to get portal session:', error);
    
    return res.status(500).json({
      success: false,
      message: 'Internal server error while retrieving portal session',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

/**
 * Update payment method for user
 */
export const handleUpdatePaymentMethod = async (req: Request, res: Response, context: any) => {
  try {
    // Validate request body
    const parsedBody = paymentMethodUpdateSchema.safeParse(req.body);
    if (!parsedBody.success) {
      return res.status(400).json({
        success: false,
        message: 'Invalid payment method update request',
        errors: parsedBody.error.format(),
      });
    }

    const { userId, paymentMethodId, isDefault, billingAddress } = parsedBody.data;

    // Verify user exists
    const user = await context.entities.User.findUnique({
      where: { id: userId },
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found',
      });
    }

    // Simulate payment method update (in production, integrate with payment processor)
    const paymentMethodUpdate = {
      userId,
      paymentMethodId,
      isDefault,
      billingAddress,
      updatedAt: new Date(),
      // Simulated payment method details
      paymentMethod: {
        id: paymentMethodId,
        type: 'card',
        last4: '4242',
        brand: 'visa',
        expiryMonth: 12,
        expiryYear: 2025,
        isDefault,
      },
    };

    console.log(`💳 Payment method updated for user ${userId}:`, paymentMethodUpdate);

    return res.status(200).json({
      success: true,
      message: 'Payment method updated successfully',
      data: paymentMethodUpdate,
    });
  } catch (error) {
    console.error('❌ Payment method update failed:', error);
    
    if (error instanceof HttpError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message,
      });
    }

    return res.status(500).json({
      success: false,
      message: 'Internal server error during payment method update',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

/**
 * Get user billing history
 */
export const handleGetBillingHistory = async (req: Request, res: Response, context: any) => {
  try {
    const { userId } = req.params;
    const { limit = 10, offset = 0 } = req.query;

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: 'User ID is required',
      });
    }

    // Verify user exists
    const user = await context.entities.User.findUnique({
      where: { id: userId },
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found',
      });
    }

    // Get user subscriptions for billing history
    const userSubscriptions = await context.entities.UserSubscription.findMany({
      where: { userId },
      include: {
        subscription: true,
      },
      orderBy: { createdAt: 'desc' },
      take: parseInt(limit as string),
      skip: parseInt(offset as string),
    });

    // Simulate billing history (in production, get from payment processor)
    const billingHistory = userSubscriptions.map((userSub: any, index) => ({
      id: `inv_${userSub.id.substring(0, 8)}`,
      subscriptionId: userSub.id,
      subscriptionName: userSub.subscription.name,
      amount: userSub.subscription.price,
      amountFormatted: `$${(userSub.subscription.price / 100).toFixed(2)}`,
      currency: 'USD',
      status: index === 0 ? 'paid' : Math.random() > 0.1 ? 'paid' : 'pending',
      invoiceDate: userSub.startDate,
      dueDate: userSub.endDate,
      paidDate: userSub.startDate,
      downloadUrl: `https://api.example.com/invoices/inv_${userSub.id.substring(0, 8)}.pdf`,
    }));

    return res.status(200).json({
      success: true,
      message: 'Billing history retrieved successfully',
      data: {
        userId,
        billingHistory,
        pagination: {
          limit: parseInt(limit as string),
          offset: parseInt(offset as string),
          total: billingHistory.length,
        },
      },
    });
  } catch (error) {
    console.error('❌ Failed to get billing history:', error);
    
    return res.status(500).json({
      success: false,
      message: 'Internal server error while retrieving billing history',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

/**
 * Generate portal URL with features and theme
 */
function generatePortalUrl(sessionId: string, features: string[], theme?: any): string {
  const baseUrl = process.env.CUSTOMER_PORTAL_BASE_URL || 'https://portal.dalti.app';
  const params = new URLSearchParams({
    session: sessionId,
    features: features.join(','),
  });

  if (theme) {
    if (theme.primaryColor) params.append('primary_color', theme.primaryColor);
    if (theme.backgroundColor) params.append('bg_color', theme.backgroundColor);
    if (theme.fontFamily) params.append('font', theme.fontFamily);
    if (theme.logoUrl) params.append('logo', theme.logoUrl);
  }

  return `${baseUrl}?${params.toString()}`;
}

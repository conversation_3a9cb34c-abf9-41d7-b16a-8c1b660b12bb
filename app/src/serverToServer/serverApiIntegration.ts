import { type Request, type Response } from 'express';
import { HttpError } from 'wasp/server';
import crypto from 'crypto';
import { z } from 'zod';
import {
  createApiKey,
  generateApiKey,
} from '../auth/externalApiAuth';

/**
 * Simple API key verification for server-to-server operations
 */
async function verifyServerApiKey(apiKey: string, entities: any): Promise<any> {
  // Hash the provided API key
  const keyHash = crypto.createHash('sha256').update(apiKey).digest('hex');

  // Find matching API key record
  const apiKeyRecord = await entities.ApiKey.findFirst({
    where: {
      keyHash,
      isActive: true,
      OR: [
        { expiresAt: null },
        { expiresAt: { gt: new Date() } },
      ],
    },
  });

  if (!apiKeyRecord) {
    throw new Error('Invalid or expired API key');
  }

  return apiKeyRecord;
}

/**
 * Server-to-Server API Integration
 * Provides API key-based authentication and endpoints for external website integration
 */

// Validation schemas
const serverApiKeySchema = z.object({
  name: z.string().min(1).max(100),
  permissions: z.array(z.enum([
    'read:users',
    'write:users',
    'read:subscriptions',
    'write:subscriptions',
    'read:payments',
    'write:payments',
    'webhook:receive',
    'webhook:send',
    'admin:all',
  ])).default(['read:users', 'read:subscriptions']),
  expiresAt: z.string().datetime().optional(),
  ipWhitelist: z.array(z.string().ip()).optional(),
  webhookEndpoints: z.array(z.string().url()).optional(),
});

const webhookSignatureSchema = z.object({
  payload: z.string(),
  signature: z.string(),
  timestamp: z.string(),
  tolerance: z.number().min(60).max(3600).default(300), // 5 minutes default
});

const serverOperationSchema = z.object({
  operation: z.enum(['create_user', 'update_subscription', 'process_payment', 'send_webhook']),
  data: z.record(z.any()),
  idempotencyKey: z.string().optional(),
});

/**
 * Create server-to-server API key
 */
export const handleCreateServerApiKey = async (req: Request, res: Response, context: any) => {
  try {
    // Validate request body
    const parsedBody = serverApiKeySchema.safeParse(req.body);
    if (!parsedBody.success) {
      return res.status(400).json({
        success: false,
        message: 'Invalid server API key configuration',
        errors: parsedBody.error.format(),
      });
    }

    const { name, permissions, expiresAt, ipWhitelist, webhookEndpoints } = parsedBody.data;

    // Create API key with server-specific permissions
    const apiKey = generateApiKey();
    const keyHash = crypto.createHash('sha256').update(apiKey).digest('hex');

    const record = await context.entities.ApiKey.create({
      data: {
        name: `[SERVER] ${name}`,
        keyHash,
        permissions,
        expiresAt: expiresAt ? new Date(expiresAt) : undefined,
        isActive: true,
      },
    });

    // Store additional server-specific metadata (in production, use a separate table)
    const serverMetadata = {
      ipWhitelist,
      webhookEndpoints,
      serverToServer: true,
      createdAt: new Date(),
    };

    console.log(`🔑 Created server API key: ${record.id}`, serverMetadata);

    return res.status(201).json({
      success: true,
      message: 'Server-to-server API key created successfully',
      data: {
        apiKey, // Only returned once!
        keyId: record.id,
        name: record.name,
        permissions: record.permissions,
        expiresAt: record.expiresAt,
        metadata: serverMetadata,
        usage: {
          rateLimit: '1000 requests/hour',
          allowedOperations: permissions,
          ipRestrictions: ipWhitelist?.length || 0,
          webhookEndpoints: webhookEndpoints?.length || 0,
        },
        createdAt: record.createdAt,
      },
    });
  } catch (error) {
    console.error('❌ Server API key creation failed:', error);
    
    if (error instanceof HttpError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message,
      });
    }

    return res.status(500).json({
      success: false,
      message: 'Internal server error during server API key creation',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

/**
 * Validate webhook signature
 */
export const handleValidateWebhookSignature = async (req: Request, res: Response, context: any) => {
  try {
    // Validate request body
    const parsedBody = webhookSignatureSchema.safeParse(req.body);
    if (!parsedBody.success) {
      return res.status(400).json({
        success: false,
        message: 'Invalid webhook signature validation request',
        errors: parsedBody.error.format(),
      });
    }

    const { payload, signature, timestamp, tolerance } = parsedBody.data;

    // Validate timestamp tolerance
    const now = Math.floor(Date.now() / 1000);
    const webhookTimestamp = parseInt(timestamp);
    
    if (Math.abs(now - webhookTimestamp) > tolerance) {
      return res.status(400).json({
        success: false,
        message: 'Webhook timestamp outside tolerance window',
        data: {
          currentTime: now,
          webhookTime: webhookTimestamp,
          tolerance,
          timeDifference: Math.abs(now - webhookTimestamp),
        },
      });
    }

    // Extract signature components
    const signatureParts = signature.split(',');
    const signatures = signatureParts.reduce((acc: Record<string, string>, part) => {
      const [key, value] = part.split('=');
      if (key && value) {
        acc[key] = value;
      }
      return acc;
    }, {});

    if (!signatures.v1) {
      return res.status(400).json({
        success: false,
        message: 'Invalid signature format. Expected v1 signature.',
      });
    }

    // Validate signature (using a test secret for demonstration)
    const testSecret = process.env.WEBHOOK_SECRET || 'test-webhook-secret-key';
    const expectedSignature = crypto
      .createHmac('sha256', testSecret)
      .update(`${timestamp}.${payload}`)
      .digest('hex');

    const isValid = signatures.v1 === expectedSignature;

    return res.status(200).json({
      success: true,
      message: 'Webhook signature validation completed',
      data: {
        isValid,
        timestamp: webhookTimestamp,
        tolerance,
        signatureVersion: 'v1',
        validatedAt: new Date(),
      },
    });
  } catch (error) {
    console.error('❌ Webhook signature validation failed:', error);
    
    return res.status(500).json({
      success: false,
      message: 'Internal server error during webhook signature validation',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

/**
 * Execute server-to-server operation
 */
export const handleServerOperation = async (req: Request, res: Response, context: any) => {
  try {
    // Extract API key from headers
    const apiKey = req.headers['x-api-key'] as string;
    if (!apiKey) {
      throw new HttpError(401, 'Server API key required in X-API-Key header');
    }

    // Verify API key
    let apiKeyRecord;
    try {
      apiKeyRecord = await verifyServerApiKey(apiKey, context.entities);
    } catch (error) {
      throw new HttpError(401, 'Invalid server API key');
    }

    // Validate request body
    const parsedBody = serverOperationSchema.safeParse(req.body);
    if (!parsedBody.success) {
      return res.status(400).json({
        success: false,
        message: 'Invalid server operation request',
        errors: parsedBody.error.format(),
      });
    }

    const { operation, data, idempotencyKey } = parsedBody.data;

    // Check permissions for the operation
    const requiredPermission = getRequiredPermissionForOperation(operation);
    if (!apiKeyRecord.permissions.includes(requiredPermission) && !apiKeyRecord.permissions.includes('admin:all')) {
      throw new HttpError(403, `Insufficient permissions. Required: ${requiredPermission}`);
    }

    // Handle idempotency (simplified - in production, use proper idempotency store)
    if (idempotencyKey) {
      console.log(`🔄 Processing operation with idempotency key: ${idempotencyKey}`);
    }

    // Execute the operation
    let result: any;
    switch (operation) {
      case 'create_user':
        result = await executeCreateUser(data, context);
        break;
      case 'update_subscription':
        result = await executeUpdateSubscription(data, context);
        break;
      case 'process_payment':
        result = await executeProcessPayment(data, context);
        break;
      case 'send_webhook':
        result = await executeSendWebhook(data, context);
        break;
      default:
        throw new HttpError(400, `Unsupported operation: ${operation}`);
    }

    return res.status(200).json({
      success: true,
      message: `Server operation '${operation}' executed successfully`,
      data: {
        operation,
        result,
        apiKeyId: apiKeyRecord.id,
        apiKeyName: apiKeyRecord.name,
        idempotencyKey,
        executedAt: new Date(),
      },
    });
  } catch (error) {
    console.error('❌ Server operation failed:', error);
    
    if (error instanceof HttpError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message,
      });
    }

    return res.status(500).json({
      success: false,
      message: 'Internal server error during server operation',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

/**
 * Get server API key usage statistics
 */
export const handleGetServerApiUsage = async (req: Request, res: Response, context: any) => {
  try {
    // Extract API key from headers
    const apiKey = req.headers['x-api-key'] as string;
    if (!apiKey) {
      throw new HttpError(401, 'Server API key required in X-API-Key header');
    }

    // Verify API key
    let apiKeyRecord;
    try {
      apiKeyRecord = await verifyServerApiKey(apiKey, context.entities);
    } catch (error) {
      throw new HttpError(401, 'Invalid server API key');
    }

    // Get usage statistics (simplified - in production, use proper analytics)
    const usageStats = {
      apiKeyId: apiKeyRecord.id,
      apiKeyName: apiKeyRecord.name,
      permissions: apiKeyRecord.permissions,
      lastUsed: apiKeyRecord.lastUsed,
      createdAt: apiKeyRecord.createdAt,
      usage: {
        totalRequests: Math.floor(Math.random() * 1000), // Simulated
        requestsToday: Math.floor(Math.random() * 100),
        requestsThisHour: Math.floor(Math.random() * 10),
        averageResponseTime: Math.floor(Math.random() * 200) + 50, // 50-250ms
        errorRate: Math.random() * 0.05, // 0-5%
      },
      limits: {
        requestsPerHour: 1000,
        requestsPerDay: 10000,
        burstLimit: 100,
      },
      status: 'active',
    };

    return res.status(200).json({
      success: true,
      message: 'Server API usage statistics retrieved successfully',
      data: usageStats,
    });
  } catch (error) {
    console.error('❌ Failed to get server API usage:', error);
    
    if (error instanceof HttpError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message,
      });
    }

    return res.status(500).json({
      success: false,
      message: 'Internal server error while retrieving API usage',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

// Helper functions

function getRequiredPermissionForOperation(operation: string): string {
  const permissionMap: Record<string, string> = {
    'create_user': 'write:users',
    'update_subscription': 'write:subscriptions',
    'process_payment': 'write:payments',
    'send_webhook': 'webhook:send',
  };
  return permissionMap[operation] || 'admin:all';
}

async function executeCreateUser(data: any, context: any): Promise<any> {
  console.log('👤 Executing create user operation');
  return { action: 'create_user', userId: 'user_' + Date.now(), email: data.email };
}

async function executeUpdateSubscription(data: any, context: any): Promise<any> {
  console.log('📋 Executing update subscription operation');
  return { action: 'update_subscription', subscriptionId: data.subscriptionId, updated: true };
}

async function executeProcessPayment(data: any, context: any): Promise<any> {
  console.log('💳 Executing process payment operation');
  return { action: 'process_payment', paymentId: 'pay_' + Date.now(), amount: data.amount };
}

async function executeSendWebhook(data: any, context: any): Promise<any> {
  console.log('📨 Executing send webhook operation');
  return { action: 'send_webhook', webhookId: 'wh_' + Date.now(), url: data.url };
}

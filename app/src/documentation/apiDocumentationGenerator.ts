import { type Request, type Response } from 'express';
import { HttpError } from 'wasp/server';

/**
 * API Documentation Generator
 * Creates comprehensive documentation for external website integration
 */

interface APIEndpoint {
  method: string;
  path: string;
  description: string;
  category: string;
  authentication: 'none' | 'jwt' | 'api-key' | 'optional';
  parameters?: Array<{
    name: string;
    type: string;
    required: boolean;
    description: string;
    example?: any;
  }>;
  requestBody?: {
    contentType: string;
    schema: any;
    example: any;
  };
  responses: Array<{
    status: number;
    description: string;
    example: any;
  }>;
  codeExamples: Array<{
    language: string;
    code: string;
  }>;
}

/**
 * Generate OpenAPI/Swagger documentation
 */
export const handleGenerateOpenAPIDoc = async (req: Request, res: Response, context: any) => {
  try {
    console.log('📚 Generating OpenAPI documentation...');

    const openApiDoc = {
      openapi: '3.0.3',
      info: {
        title: 'Dalti Subscription Management API',
        description: 'Comprehensive API for external website integration with subscription management system',
        version: '1.0.0',
        contact: {
          name: 'Dalti API Support',
          email: '<EMAIL>',
          url: 'https://docs.dalti.app',
        },
        license: {
          name: 'MIT',
          url: 'https://opensource.org/licenses/MIT',
        },
      },
      servers: [
        {
          url: 'https://api.dalti.app',
          description: 'Production server',
        },
        {
          url: 'https://api-staging.dalti.app',
          description: 'Staging server',
        },
        {
          url: 'http://localhost:5400',
          description: 'Development server',
        },
      ],
      security: [
        { ApiKeyAuth: [] },
        { BearerAuth: [] },
      ],
      components: {
        securitySchemes: {
          ApiKeyAuth: {
            type: 'apiKey',
            in: 'header',
            name: 'X-API-Key',
            description: 'API key for server-to-server authentication',
          },
          BearerAuth: {
            type: 'http',
            scheme: 'bearer',
            bearerFormat: 'JWT',
            description: 'JWT token for user authentication',
          },
        },
        schemas: {
          User: {
            type: 'object',
            properties: {
              id: { type: 'string', format: 'uuid' },
              email: { type: 'string', format: 'email' },
              credits: { type: 'integer', minimum: 0 },
              subscriptionStatus: { type: 'string', enum: ['active', 'cancelled', 'expired'] },
              createdAt: { type: 'string', format: 'date-time' },
            },
          },
          Subscription: {
            type: 'object',
            properties: {
              id: { type: 'string', format: 'uuid' },
              name: { type: 'string' },
              description: { type: 'string' },
              price: { type: 'integer', description: 'Price in cents' },
              interval: { type: 'string', enum: ['monthly', 'yearly'] },
              creditsIncluded: { type: 'integer' },
              features: { type: 'array', items: { type: 'string' } },
              isActive: { type: 'boolean' },
            },
          },
          CheckoutSession: {
            type: 'object',
            properties: {
              sessionId: { type: 'string' },
              sessionUrl: { type: 'string', format: 'uri' },
              planIdentifier: { type: 'string' },
              returnUrl: { type: 'string', format: 'uri' },
              cancelUrl: { type: 'string', format: 'uri' },
              expiresAt: { type: 'string', format: 'date-time' },
            },
          },
          Error: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: false },
              message: { type: 'string' },
              error: { type: 'string' },
            },
          },
        },
      },
      paths: generateOpenAPIPaths(),
      tags: [
        { name: 'Authentication', description: 'API key and JWT authentication' },
        { name: 'Subscriptions', description: 'Subscription management operations' },
        { name: 'Checkout', description: 'Payment and checkout operations' },
        { name: 'Users', description: 'User management operations' },
        { name: 'Webhooks', description: 'Webhook management and callbacks' },
        { name: 'Portal', description: 'Customer portal integration' },
        { name: 'Transition', description: 'System transition and feature flags' },
      ],
    };

    return res.status(200).json({
      success: true,
      message: 'OpenAPI documentation generated successfully',
      data: openApiDoc,
    });
  } catch (error) {
    console.error('❌ OpenAPI documentation generation failed:', error);
    
    return res.status(500).json({
      success: false,
      message: 'Internal server error during documentation generation',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

/**
 * Generate integration guide
 */
export const handleGenerateIntegrationGuide = async (req: Request, res: Response, context: any) => {
  try {
    console.log('📖 Generating integration guide...');

    const integrationGuide = {
      title: 'Dalti Subscription API Integration Guide',
      version: '1.0.0',
      lastUpdated: new Date(),
      sections: [
        {
          title: 'Getting Started',
          content: `
# Getting Started with Dalti Subscription API

## Overview
The Dalti Subscription API provides comprehensive subscription management capabilities for external websites. This guide will help you integrate subscription functionality into your application.

## Base URLs
- Production: \`https://api.dalti.app\`
- Staging: \`https://api-staging.dalti.app\`
- Development: \`http://localhost:5400\`

## Authentication
The API supports two authentication methods:
1. **API Keys** - For server-to-server operations
2. **JWT Tokens** - For user-specific operations

### API Key Authentication
Include your API key in the request header:
\`\`\`
X-API-Key: your-api-key-here
\`\`\`

### JWT Authentication
Include the JWT token in the Authorization header:
\`\`\`
Authorization: Bearer your-jwt-token-here
\`\`\`
          `,
        },
        {
          title: 'Quick Start Examples',
          content: `
# Quick Start Examples

## 1. Get Available Subscriptions
\`\`\`javascript
const response = await fetch('https://api.dalti.app/api/external/subscriptions');
const data = await response.json();
console.log(data.data.subscriptions);
\`\`\`

## 2. Create Checkout Session
\`\`\`javascript
const checkoutData = {
  planIdentifier: 'hobby',
  returnUrl: 'https://yoursite.com/success',
  cancelUrl: 'https://yoursite.com/cancel',
  customerInfo: {
    email: '<EMAIL>',
    name: 'John Doe'
  }
};

const response = await fetch('https://api.dalti.app/api/external/checkout', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify(checkoutData)
});

const checkout = await response.json();
window.location.href = checkout.data.sessionUrl;
\`\`\`

## 3. Handle Webhooks
\`\`\`javascript
// Register webhook endpoint
const webhookConfig = {
  url: 'https://yoursite.com/webhooks/dalti',
  secret: 'your-webhook-secret',
  events: ['subscription.created', 'payment.succeeded']
};

await fetch('https://api.dalti.app/api/webhooks/callbacks', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify(webhookConfig)
});
\`\`\`
          `,
        },
        {
          title: 'Common Integration Patterns',
          content: `
# Common Integration Patterns

## 1. Subscription Management Widget
Create a subscription management widget for your website:

\`\`\`html
<div id="subscription-widget">
  <div id="subscription-plans"></div>
  <div id="current-subscription"></div>
</div>
\`\`\`

\`\`\`javascript
class SubscriptionWidget {
  constructor(containerId, apiKey) {
    this.container = document.getElementById(containerId);
    this.apiKey = apiKey;
    this.init();
  }

  async init() {
    await this.loadSubscriptionPlans();
    await this.loadCurrentSubscription();
  }

  async loadSubscriptionPlans() {
    const response = await fetch('/api/external/subscriptions');
    const data = await response.json();
    this.renderPlans(data.data.subscriptions);
  }

  renderPlans(plans) {
    const plansContainer = this.container.querySelector('#subscription-plans');
    plansContainer.innerHTML = plans.map(plan => \`
      <div class="plan-card" data-plan-id="\${plan.id}">
        <h3>\${plan.name}</h3>
        <p>\${plan.description}</p>
        <div class="price">\${plan.priceFormatted}/\${plan.interval}</div>
        <button onclick="this.selectPlan('\${plan.id}')">Select Plan</button>
      </div>
    \`).join('');
  }

  async selectPlan(planId) {
    const checkoutData = {
      planIdentifier: planId,
      returnUrl: window.location.origin + '/success',
      cancelUrl: window.location.origin + '/cancel',
      customerInfo: {
        email: this.getCurrentUserEmail()
      }
    };

    const response = await fetch('/api/external/checkout', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(checkoutData)
    });

    const checkout = await response.json();
    if (checkout.success) {
      window.location.href = checkout.data.sessionUrl;
    }
  }
}

// Initialize widget
const widget = new SubscriptionWidget('subscription-widget', 'your-api-key');
\`\`\`

## 2. Customer Portal Integration
\`\`\`javascript
async function openCustomerPortal(userEmail) {
  const portalData = {
    email: userEmail,
    returnUrl: window.location.href,
    features: ['subscription_management', 'billing_history', 'payment_methods'],
    theme: {
      primaryColor: '#007bff',
      logoUrl: 'https://yoursite.com/logo.png'
    }
  };

  const response = await fetch('/api/portal/sessions', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(portalData)
  });

  const portal = await response.json();
  if (portal.success) {
    window.open(portal.data.portalUrl, '_blank');
  }
}
\`\`\`
          `,
        },
        {
          title: 'Error Handling',
          content: `
# Error Handling

## Standard Error Response Format
All API endpoints return errors in a consistent format:

\`\`\`json
{
  "success": false,
  "message": "Human-readable error message",
  "error": "Technical error details",
  "errors": {
    // Validation errors (if applicable)
  }
}
\`\`\`

## Common HTTP Status Codes
- \`200\` - Success
- \`201\` - Created
- \`400\` - Bad Request (validation errors)
- \`401\` - Unauthorized (authentication required)
- \`403\` - Forbidden (insufficient permissions)
- \`404\` - Not Found
- \`429\` - Rate Limit Exceeded
- \`500\` - Internal Server Error

## Error Handling Best Practices
\`\`\`javascript
async function apiCall(endpoint, options = {}) {
  try {
    const response = await fetch(endpoint, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(\`API Error (\${response.status}): \${data.message}\`);
    }

    if (!data.success) {
      throw new Error(data.message || 'API request failed');
    }

    return data.data;
  } catch (error) {
    console.error('API call failed:', error);
    throw error;
  }
}

// Usage
try {
  const subscriptions = await apiCall('/api/external/subscriptions');
  console.log('Subscriptions loaded:', subscriptions);
} catch (error) {
  // Handle error appropriately
  showErrorMessage('Failed to load subscriptions: ' + error.message);
}
\`\`\`
          `,
        },
        {
          title: 'Webhook Security',
          content: `
# Webhook Security

## Signature Verification
All webhooks include a signature for verification:

\`\`\`javascript
const crypto = require('crypto');

function verifyWebhookSignature(payload, signature, secret) {
  const expectedSignature = crypto
    .createHmac('sha256', secret)
    .update(payload)
    .digest('hex');

  return crypto.timingSafeEqual(
    Buffer.from(signature, 'hex'),
    Buffer.from(expectedSignature, 'hex')
  );
}

// Express.js webhook handler
app.post('/webhooks/dalti', express.raw({type: 'application/json'}), (req, res) => {
  const signature = req.headers['x-webhook-signature'];
  const payload = req.body;

  if (!verifyWebhookSignature(payload, signature, process.env.WEBHOOK_SECRET)) {
    return res.status(401).send('Invalid signature');
  }

  const event = JSON.parse(payload);
  console.log('Received webhook:', event.eventType);

  // Process the webhook event
  handleWebhookEvent(event);

  res.status(200).send('OK');
});
\`\`\`

## Webhook Event Types
- \`subscription.created\` - New subscription created
- \`subscription.updated\` - Subscription modified
- \`subscription.cancelled\` - Subscription cancelled
- \`subscription.expired\` - Subscription expired
- \`payment.succeeded\` - Payment completed successfully
- \`payment.failed\` - Payment failed
          `,
        },
      ],
    };

    return res.status(200).json({
      success: true,
      message: 'Integration guide generated successfully',
      data: integrationGuide,
    });
  } catch (error) {
    console.error('❌ Integration guide generation failed:', error);
    
    return res.status(500).json({
      success: false,
      message: 'Internal server error during guide generation',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

/**
 * Generate OpenAPI paths
 */
function generateOpenAPIPaths(): any {
  return {
    '/api/external/subscriptions': {
      get: {
        tags: ['Subscriptions'],
        summary: 'Get available subscriptions',
        description: 'Retrieve all active subscription plans available for purchase',
        responses: {
          200: {
            description: 'Successful response',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    success: { type: 'boolean' },
                    message: { type: 'string' },
                    data: {
                      type: 'object',
                      properties: {
                        subscriptions: {
                          type: 'array',
                          items: { $ref: '#/components/schemas/Subscription' }
                        },
                        total: { type: 'integer' }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    },
    '/api/external/checkout': {
      post: {
        tags: ['Checkout'],
        summary: 'Create checkout session',
        description: 'Create a new checkout session for subscription purchase',
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                required: ['planIdentifier', 'returnUrl', 'cancelUrl'],
                properties: {
                  planIdentifier: { type: 'string' },
                  returnUrl: { type: 'string', format: 'uri' },
                  cancelUrl: { type: 'string', format: 'uri' },
                  customerInfo: {
                    type: 'object',
                    properties: {
                      email: { type: 'string', format: 'email' },
                      name: { type: 'string' }
                    }
                  }
                }
              }
            }
          }
        },
        responses: {
          201: {
            description: 'Checkout session created',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    success: { type: 'boolean' },
                    message: { type: 'string' },
                    data: { $ref: '#/components/schemas/CheckoutSession' }
                  }
                }
              }
            }
          }
        }
      }
    }
  };
}

import { type Request, type Response } from 'express';
import { HttpError } from 'wasp/server';
import {
  createApi<PERSON>ey,
  generateApi<PERSON>ey,
  createExternalApiAuthMiddleware,
  requireAuth,
  requireApiKey,
  requireJWT,
  type AuthenticatedRequest,
} from './externalApiAuth';

/**
 * Comprehensive test for the external API authentication system
 * Tests both JWT and API key authentication with various scenarios
 */
export const handleTestAuthSystem = async (req: Request, res: Response, context: any) => {
  try {
    console.log('🧪 Starting comprehensive authentication system test...');
    console.log('Context entities:', Object.keys(context.entities || {}));

    const testResults: any[] = [];

    // Test 1: Test authentication middleware functionality
    try {
      console.log('🔑 Test 1: Testing authentication middleware...');

      // Test basic middleware creation
      const basicAuthMiddleware = createExternalApiAuthMiddleware({
        requireAuth: false,
        allowedAuthTypes: ['jwt', 'api-key'],
      });

      testResults.push({
        test: 'middleware_creation',
        success: true,
        data: {
          middlewareType: 'external_api_auth',
          requireAuth: false,
          allowedAuthTypes: ['jwt', 'api-key'],
        },
      });

      // Test 2: Test API key generation
      console.log('🔐 Test 2: Testing API key generation...');

      const generatedApiKey = generateApiKey();

      testResults.push({
        test: 'api_key_generation',
        success: true,
        data: {
          apiKeyLength: generatedApiKey.length,
          apiKeyPreview: `${generatedApiKey.substring(0, 8)}...`,
          isHex: /^[a-f0-9]+$/i.test(generatedApiKey),
        },
      });

      // Test 3: Test middleware factory functions
      console.log('🛡️ Test 3: Testing middleware factory functions...');

      try {
        const authMiddlewares = {
          requireAuth: requireAuth(),
          optionalAuth: requireAuth({ requireAuth: false }),
          requireApiKey: requireApiKey(),
          requireJWT: requireJWT(),
        };

        testResults.push({
          test: 'middleware_factories',
          success: true,
          data: {
            factoriesCreated: Object.keys(authMiddlewares).length,
            types: Object.keys(authMiddlewares),
          },
        });
      } catch (error) {
        testResults.push({
          test: 'middleware_factories',
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }

      // Test 4: Test authentication type detection
      console.log('🔍 Test 4: Testing authentication type detection...');

      try {
        const testHeaders = [
          { 'authorization': 'Bearer jwt-token-123' },
          { 'x-api-key': 'api-key-123' },
          { 'authorization': 'ApiKey api-key-456' },
          {},
        ];

        const detectionResults = testHeaders.map((headers, index) => {
          const hasJWT = headers.authorization?.startsWith('Bearer ');
          const hasApiKey = headers['x-api-key'] || headers.authorization?.startsWith('ApiKey ');

          return {
            testCase: index + 1,
            headers,
            detectedJWT: !!hasJWT,
            detectedApiKey: !!hasApiKey,
          };
        });

        testResults.push({
          test: 'auth_type_detection',
          success: true,
          data: {
            testCases: detectionResults.length,
            results: detectionResults,
          },
        });
      } catch (error) {
        testResults.push({
          test: 'auth_type_detection',
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }

      // Test 5: Test configuration validation
      console.log('⚙️ Test 5: Testing configuration validation...');

      try {
        const configurations = [
          { requireAuth: true, allowedAuthTypes: ['jwt' as const] },
          { requireAuth: false, allowedAuthTypes: ['api-key' as const] },
          { requireAuth: true, allowedAuthTypes: ['jwt' as const, 'api-key' as const] },
          {
            requireAuth: true,
            allowedAuthTypes: ['api-key' as const],
            requiredPermissions: ['read', 'write'],
            rateLimit: { requests: 100, windowMs: 60000 }
          },
        ];

        const configResults = configurations.map((config, index) => {
          try {
            const middleware = createExternalApiAuthMiddleware(config);
            return {
              configIndex: index + 1,
              config,
              success: true,
            };
          } catch (error) {
            return {
              configIndex: index + 1,
              config,
              success: false,
              error: error instanceof Error ? error.message : 'Unknown error',
            };
          }
        });

        testResults.push({
          test: 'configuration_validation',
          success: true,
          data: {
            totalConfigurations: configurations.length,
            results: configResults,
            successfulConfigurations: configResults.filter(r => r.success).length,
          },
        });
      } catch (error) {
        testResults.push({
          test: 'configuration_validation',
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }

    } catch (error) {
      testResults.push({
        test: 'setup_error',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }

    // Calculate summary
    const successCount = testResults.filter(r => r.success).length;
    const totalCount = testResults.length;

    return res.status(200).json({
      success: true,
      message: 'Authentication system test completed',
      data: {
        summary: {
          passed: successCount,
          total: totalCount,
          successRate: `${Math.round((successCount / totalCount) * 100)}%`,
        },
        results: testResults,
        timestamp: new Date(),
      },
    });

  } catch (error) {
    console.error('❌ Authentication system test failed:', error);
    
    return res.status(500).json({
      success: false,
      message: 'Internal server error during authentication system test',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

import { type Request, type Response, type NextFunction } from 'express';
import { HttpError } from 'wasp/server';
import jwt from 'jsonwebtoken';
import crypto from 'crypto';
import { PrismaClient } from '@prisma/client';

/**
 * External API Authentication System
 * Supports both JWT tokens for user operations and API keys for server-to-server operations
 */

export interface ApiKeyRecord {
  id: string;
  name: string;
  keyHash: string;
  userId?: string;
  permissions: string[];
  isActive: boolean;
  lastUsed?: Date;
  expiresAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface AuthenticatedRequest extends Request {
  user?: any;
  apiKey?: ApiKeyRecord;
  authType: 'jwt' | 'api-key' | 'none';
}

export interface RateLimitInfo {
  requests: number;
  resetTime: Date;
  limit: number;
}

// In-memory rate limiting store (in production, use Redis)
const rateLimitStore = new Map<string, RateLimitInfo>();

/**
 * Enhanced authentication middleware for external APIs
 * Supports both JWT and API key authentication
 */
export function createExternalApiAuthMiddleware(options: {
  requireAuth?: boolean;
  allowedAuthTypes?: ('jwt' | 'api-key')[];
  requiredPermissions?: string[];
  rateLimit?: {
    requests: number;
    windowMs: number;
  };
} = {}) {
  const {
    requireAuth = true,
    allowedAuthTypes = ['jwt', 'api-key'],
    requiredPermissions = [],
    rateLimit,
  } = options;

  return async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      req.authType = 'none';

      // Check for JWT token authentication
      if (allowedAuthTypes.includes('jwt')) {
        const jwtToken = extractJwtToken(req);
        if (jwtToken) {
          try {
            const decoded = verifyJwtToken(jwtToken);
            req.user = decoded;
            req.authType = 'jwt';
            console.log(`✅ JWT authentication successful for user: ${decoded.id}`);
          } catch (error) {
            console.warn('❌ JWT token verification failed:', error);
            if (requireAuth && !allowedAuthTypes.includes('api-key')) {
              throw new HttpError(401, 'Invalid JWT token');
            }
          }
        }
      }

      // Check for API key authentication if JWT failed or not provided
      if (req.authType === 'none' && allowedAuthTypes.includes('api-key')) {
        const apiKey = extractApiKey(req);
        if (apiKey) {
          try {
            const apiKeyRecord = await verifyApiKey(apiKey, req.app.locals.prisma);
            req.apiKey = apiKeyRecord;
            req.authType = 'api-key';
            console.log(`✅ API key authentication successful: ${apiKeyRecord.name}`);
            
            // Update last used timestamp
            await updateApiKeyLastUsed(apiKeyRecord.id, req.app.locals.prisma);
          } catch (error) {
            console.warn('❌ API key verification failed:', error);
            if (requireAuth) {
              throw new HttpError(401, 'Invalid API key');
            }
          }
        }
      }

      // Check if authentication is required
      if (requireAuth && req.authType === 'none') {
        throw new HttpError(401, 'Authentication required. Provide either JWT token or API key.');
      }

      // Check permissions
      if (requiredPermissions.length > 0) {
        const hasPermission = checkPermissions(req, requiredPermissions);
        if (!hasPermission) {
          throw new HttpError(403, `Insufficient permissions. Required: ${requiredPermissions.join(', ')}`);
        }
      }

      // Apply rate limiting
      if (rateLimit) {
        const identifier = getRequestIdentifier(req);
        const isAllowed = checkRateLimit(identifier, rateLimit);
        if (!isAllowed) {
          throw new HttpError(429, 'Rate limit exceeded. Please try again later.');
        }
      }

      next();
    } catch (error) {
      if (error instanceof HttpError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
          authType: req.authType,
        });
      } else {
        console.error('❌ Authentication middleware error:', error);
        res.status(500).json({
          success: false,
          message: 'Internal authentication error',
        });
      }
    }
  };
}

/**
 * Extract JWT token from request headers
 */
function extractJwtToken(req: Request): string | null {
  const authHeader = req.headers.authorization;
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }
  return null;
}

/**
 * Extract API key from request headers
 */
function extractApiKey(req: Request): string | null {
  // Check X-API-Key header
  const apiKeyHeader = req.headers['x-api-key'] as string;
  if (apiKeyHeader) {
    return apiKeyHeader;
  }

  // Check Authorization header with API key format
  const authHeader = req.headers.authorization;
  if (authHeader && authHeader.startsWith('ApiKey ')) {
    return authHeader.substring(7);
  }

  return null;
}

/**
 * Verify JWT token
 */
function verifyJwtToken(token: string): any {
  const jwtSecret = process.env.JWT_SECRET;
  if (!jwtSecret) {
    throw new Error('JWT_SECRET environment variable not set');
  }

  return jwt.verify(token, jwtSecret);
}

/**
 * Verify API key against database
 */
async function verifyApiKey(apiKey: string, prisma: PrismaClient): Promise<ApiKeyRecord> {
  // Hash the provided API key
  const keyHash = hashApiKey(apiKey);

  // Find matching API key record
  const apiKeyRecord = await prisma.apiKey.findFirst({
    where: {
      keyHash,
      isActive: true,
      OR: [
        { expiresAt: null },
        { expiresAt: { gt: new Date() } },
      ],
    },
  });

  if (!apiKeyRecord) {
    throw new Error('Invalid or expired API key');
  }

  return apiKeyRecord as ApiKeyRecord;
}

/**
 * Update API key last used timestamp
 */
async function updateApiKeyLastUsed(apiKeyId: string, prisma: PrismaClient): Promise<void> {
  try {
    await prisma.apiKey.update({
      where: { id: apiKeyId },
      data: { lastUsed: new Date() },
    });
  } catch (error) {
    console.warn('Failed to update API key last used timestamp:', error);
  }
}

/**
 * Check if user/API key has required permissions
 */
function checkPermissions(req: AuthenticatedRequest, requiredPermissions: string[]): boolean {
  if (req.authType === 'jwt') {
    // For JWT tokens, check user permissions (implement based on your user model)
    return true; // Simplified - implement proper permission checking
  }

  if (req.authType === 'api-key' && req.apiKey) {
    // Check if API key has all required permissions
    return requiredPermissions.every(permission => 
      req.apiKey!.permissions.includes(permission) || req.apiKey!.permissions.includes('*')
    );
  }

  return false;
}

/**
 * Get request identifier for rate limiting
 */
function getRequestIdentifier(req: AuthenticatedRequest): string {
  if (req.authType === 'jwt' && req.user) {
    return `jwt:${req.user.id}`;
  }

  if (req.authType === 'api-key' && req.apiKey) {
    return `api-key:${req.apiKey.id}`;
  }

  // Fallback to IP address
  return `ip:${req.ip || req.connection.remoteAddress}`;
}

/**
 * Check rate limit for identifier
 */
function checkRateLimit(identifier: string, rateLimit: { requests: number; windowMs: number }): boolean {
  const now = new Date();
  const windowStart = new Date(now.getTime() - rateLimit.windowMs);

  let rateLimitInfo = rateLimitStore.get(identifier);

  if (!rateLimitInfo || rateLimitInfo.resetTime <= now) {
    // Create new rate limit window
    rateLimitInfo = {
      requests: 1,
      resetTime: new Date(now.getTime() + rateLimit.windowMs),
      limit: rateLimit.requests,
    };
    rateLimitStore.set(identifier, rateLimitInfo);
    return true;
  }

  if (rateLimitInfo.requests >= rateLimit.requests) {
    return false; // Rate limit exceeded
  }

  // Increment request count
  rateLimitInfo.requests++;
  rateLimitStore.set(identifier, rateLimitInfo);
  return true;
}

/**
 * Hash API key for secure storage
 */
function hashApiKey(apiKey: string): string {
  return crypto.createHash('sha256').update(apiKey).digest('hex');
}

/**
 * Generate new API key
 */
export function generateApiKey(): string {
  return crypto.randomBytes(32).toString('hex');
}

/**
 * Create API key record
 */
export async function createApiKey(
  data: {
    name: string;
    userId?: string;
    permissions: string[];
    expiresAt?: Date;
  },
  prisma: PrismaClient
): Promise<{ apiKey: string; record: ApiKeyRecord }> {
  const apiKey = generateApiKey();
  const keyHash = hashApiKey(apiKey);

  const record = await prisma.apiKey.create({
    data: {
      name: data.name,
      keyHash,
      userId: data.userId,
      permissions: data.permissions,
      expiresAt: data.expiresAt,
      isActive: true,
    },
  });

  return {
    apiKey,
    record: record as ApiKeyRecord,
  };
}

/**
 * Revoke API key
 */
export async function revokeApiKey(apiKeyId: string, prisma: PrismaClient): Promise<void> {
  await prisma.apiKey.update({
    where: { id: apiKeyId },
    data: { isActive: false },
  });
}

// Export middleware factory for easy use
export const requireAuth = (options?: Parameters<typeof createExternalApiAuthMiddleware>[0]) =>
  createExternalApiAuthMiddleware({ requireAuth: true, ...options });

export const optionalAuth = (options?: Parameters<typeof createExternalApiAuthMiddleware>[0]) =>
  createExternalApiAuthMiddleware({ requireAuth: false, ...options });

export const requireApiKey = (options?: Parameters<typeof createExternalApiAuthMiddleware>[0]) =>
  createExternalApiAuthMiddleware({ 
    requireAuth: true, 
    allowedAuthTypes: ['api-key'],
    ...options 
  });

export const requireJWT = (options?: Parameters<typeof createExternalApiAuthMiddleware>[0]) =>
  createExternalApiAuthMiddleware({ 
    requireAuth: true, 
    allowedAuthTypes: ['jwt'],
    ...options 
  });

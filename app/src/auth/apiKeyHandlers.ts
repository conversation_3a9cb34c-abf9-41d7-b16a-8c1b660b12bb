import { type Request, type Response } from 'express';
import { HttpError } from 'wasp/server';
import { z } from 'zod';
import {
  createApiKey,
  revokeApiKey,
  generateApiKey,
  type ApiKeyRecord,
} from './externalApiAuth';

/**
 * API handlers for managing API keys
 */

// Validation schemas
const createApiKeySchema = z.object({
  name: z.string().min(1).max(100),
  permissions: z.array(z.string()).default(['read']),
  expiresAt: z.string().datetime().optional(),
});

const revokeApiKeySchema = z.object({
  apiKeyId: z.string().uuid(),
});

/**
 * Create a new API key
 */
export const handleCreateApiKey = async (req: Request, res: Response, context: any) => {
  try {
    // Check authentication
    if (!context.user) {
      throw new HttpError(401, 'User not authenticated');
    }

    // Validate request body
    const parsedBody = createApiKeySchema.safeParse(req.body);
    if (!parsedBody.success) {
      return res.status(400).json({
        success: false,
        message: 'Invalid request body',
        errors: parsedBody.error.format(),
      });
    }

    const { name, permissions, expiresAt } = parsedBody.data;
    const userId = context.user.id;

    // Create API key
    const { apiKey, record } = await createApiKey(
      {
        name,
        userId,
        permissions,
        expiresAt: expiresAt ? new Date(expiresAt) : undefined,
      },
      context.entities
    );

    return res.status(201).json({
      success: true,
      message: 'API key created successfully',
      data: {
        apiKey, // Only return this once!
        record: {
          id: record.id,
          name: record.name,
          permissions: record.permissions,
          isActive: record.isActive,
          expiresAt: record.expiresAt,
          createdAt: record.createdAt,
        },
      },
    });
  } catch (error) {
    console.error('❌ API key creation failed:', error);
    
    if (error instanceof HttpError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message,
      });
    }

    return res.status(500).json({
      success: false,
      message: 'Internal server error during API key creation',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

/**
 * Get user's API keys
 */
export const handleGetUserApiKeys = async (req: Request, res: Response, context: any) => {
  try {
    // Check authentication
    if (!context.user) {
      throw new HttpError(401, 'User not authenticated');
    }

    const userId = context.user.id;

    // Get user's API keys (without the actual key values)
    const apiKeys = await context.entities.ApiKey.findMany({
      where: { userId },
      select: {
        id: true,
        name: true,
        permissions: true,
        isActive: true,
        lastUsed: true,
        expiresAt: true,
        createdAt: true,
        updatedAt: true,
      },
      orderBy: { createdAt: 'desc' },
    });

    return res.status(200).json({
      success: true,
      message: 'API keys retrieved successfully',
      data: {
        apiKeys,
        total: apiKeys.length,
      },
    });
  } catch (error) {
    console.error('❌ Failed to get API keys:', error);
    
    if (error instanceof HttpError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message,
      });
    }

    return res.status(500).json({
      success: false,
      message: 'Internal server error while retrieving API keys',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

/**
 * Revoke an API key
 */
export const handleRevokeApiKey = async (req: Request, res: Response, context: any) => {
  try {
    // Check authentication
    if (!context.user) {
      throw new HttpError(401, 'User not authenticated');
    }

    // Validate request body
    const parsedBody = revokeApiKeySchema.safeParse(req.body);
    if (!parsedBody.success) {
      return res.status(400).json({
        success: false,
        message: 'Invalid request body',
        errors: parsedBody.error.format(),
      });
    }

    const { apiKeyId } = parsedBody.data;
    const userId = context.user.id;

    // Check if API key belongs to user
    const apiKey = await context.entities.ApiKey.findFirst({
      where: {
        id: apiKeyId,
        userId,
      },
    });

    if (!apiKey) {
      throw new HttpError(404, 'API key not found or does not belong to user');
    }

    // Revoke API key
    await revokeApiKey(apiKeyId, context.entities);

    return res.status(200).json({
      success: true,
      message: 'API key revoked successfully',
      data: {
        apiKeyId,
        revokedAt: new Date(),
      },
    });
  } catch (error) {
    console.error('❌ API key revocation failed:', error);
    
    if (error instanceof HttpError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message,
      });
    }

    return res.status(500).json({
      success: false,
      message: 'Internal server error during API key revocation',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

/**
 * Test API key authentication
 */
export const handleTestApiKeyAuth = async (req: Request, res: Response, context: any) => {
  try {
    // This endpoint will be protected by the API key middleware
    // If we reach here, authentication was successful
    
    const authType = (req as any).authType;
    const user = (req as any).user;
    const apiKey = (req as any).apiKey;

    return res.status(200).json({
      success: true,
      message: 'API key authentication successful',
      data: {
        authType,
        user: user ? {
          id: user.id,
          email: user.email,
        } : null,
        apiKey: apiKey ? {
          id: apiKey.id,
          name: apiKey.name,
          permissions: apiKey.permissions,
          lastUsed: apiKey.lastUsed,
        } : null,
        timestamp: new Date(),
      },
    });
  } catch (error) {
    console.error('❌ API key authentication test failed:', error);
    
    return res.status(500).json({
      success: false,
      message: 'Internal server error during authentication test',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

/**
 * Test rate limiting
 */
export const handleTestRateLimit = async (req: Request, res: Response, context: any) => {
  try {
    const authType = (req as any).authType;
    const requestCount = Math.floor(Math.random() * 100) + 1; // Simulate request count

    return res.status(200).json({
      success: true,
      message: 'Rate limit test successful',
      data: {
        authType,
        requestCount,
        timestamp: new Date(),
        rateLimitInfo: {
          limit: 100,
          remaining: Math.max(0, 100 - requestCount),
          resetTime: new Date(Date.now() + 60000), // 1 minute from now
        },
      },
    });
  } catch (error) {
    console.error('❌ Rate limit test failed:', error);
    
    return res.status(500).json({
      success: false,
      message: 'Internal server error during rate limit test',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

import { type Request, type Response } from 'express';
import { HttpError } from 'wasp/server';
import { z } from 'zod';

/**
 * System Transition Manager
 * Manages gradual transition from legacy to new subscription system
 * Includes feature flags, A/B testing, and rollback capabilities
 */

// Validation schemas
const featureFlagSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().max(500),
  enabled: z.boolean().default(false),
  rolloutPercentage: z.number().min(0).max(100).default(0),
  targetUsers: z.array(z.string()).optional(),
  excludeUsers: z.array(z.string()).optional(),
  environment: z.enum(['development', 'staging', 'production']).default('development'),
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
});

const abTestSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().max(500),
  variants: z.array(z.object({
    name: z.string(),
    percentage: z.number().min(0).max(100),
    config: z.record(z.any()),
  })),
  targetAudience: z.object({
    userSegments: z.array(z.string()).optional(),
    geoTargeting: z.array(z.string()).optional(),
    deviceTypes: z.array(z.string()).optional(),
  }).optional(),
  metrics: z.array(z.string()).default(['conversion_rate', 'user_engagement']),
  duration: z.number().min(1).max(90).default(14), // days
});

const transitionConfigSchema = z.object({
  phase: z.enum(['planning', 'pilot', 'gradual_rollout', 'full_migration', 'cleanup']),
  rolloutPercentage: z.number().min(0).max(100),
  enabledFeatures: z.array(z.string()),
  rollbackThreshold: z.number().min(0).max(100).default(5), // error rate %
  monitoringEnabled: z.boolean().default(true),
});

interface FeatureFlag {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
  rolloutPercentage: number;
  targetUsers?: string[];
  excludeUsers?: string[];
  environment: string;
  startDate?: Date;
  endDate?: Date;
  createdAt: Date;
  updatedAt: Date;
}

interface ABTest {
  id: string;
  name: string;
  description: string;
  variants: Array<{
    name: string;
    percentage: number;
    config: Record<string, any>;
  }>;
  targetAudience?: {
    userSegments?: string[];
    geoTargeting?: string[];
    deviceTypes?: string[];
  };
  metrics: string[];
  duration: number;
  status: 'draft' | 'running' | 'completed' | 'paused';
  startDate?: Date;
  endDate?: Date;
  results?: Record<string, any>;
  createdAt: Date;
}

// In-memory storage (in production, use database)
const featureFlags = new Map<string, FeatureFlag>();
const abTests = new Map<string, ABTest>();
const transitionMetrics = new Map<string, any>();

/**
 * Create or update feature flag
 */
export const handleManageFeatureFlag = async (req: Request, res: Response, context: any) => {
  try {
    // Validate request body
    const parsedBody = featureFlagSchema.safeParse(req.body);
    if (!parsedBody.success) {
      return res.status(400).json({
        success: false,
        message: 'Invalid feature flag configuration',
        errors: parsedBody.error.format(),
      });
    }

    const flagData = parsedBody.data;
    const { flagId } = req.query;

    // Create or update feature flag
    const flagIdToUse = (flagId as string) || `flag_${Date.now()}`;
    const now = new Date();

    const featureFlag: FeatureFlag = {
      id: flagIdToUse,
      name: flagData.name,
      description: flagData.description,
      enabled: flagData.enabled,
      rolloutPercentage: flagData.rolloutPercentage,
      targetUsers: flagData.targetUsers,
      excludeUsers: flagData.excludeUsers,
      environment: flagData.environment,
      startDate: flagData.startDate ? new Date(flagData.startDate) : undefined,
      endDate: flagData.endDate ? new Date(flagData.endDate) : undefined,
      createdAt: featureFlags.get(flagIdToUse)?.createdAt || now,
      updatedAt: now,
    };

    featureFlags.set(flagIdToUse, featureFlag);

    console.log(`🚩 Feature flag ${flagId ? 'updated' : 'created'}: ${featureFlag.name}`);

    return res.status(flagId ? 200 : 201).json({
      success: true,
      message: `Feature flag ${flagId ? 'updated' : 'created'} successfully`,
      data: featureFlag,
    });
  } catch (error) {
    console.error('❌ Feature flag management failed:', error);
    
    return res.status(500).json({
      success: false,
      message: 'Internal server error during feature flag management',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

/**
 * Get feature flag status for user
 */
export const handleGetFeatureFlagStatus = async (req: Request, res: Response, context: any) => {
  try {
    const { userId, environment = 'production' } = req.query;

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: 'User ID is required',
      });
    }

    // Get all active feature flags for environment
    const activeFlags = Array.from(featureFlags.values()).filter(flag => 
      flag.environment === environment && 
      flag.enabled &&
      (!flag.startDate || flag.startDate <= new Date()) &&
      (!flag.endDate || flag.endDate >= new Date())
    );

    // Evaluate flags for user
    const userFlags: Record<string, boolean> = {};
    
    for (const flag of activeFlags) {
      const isEnabled = evaluateFeatureFlagForUser(flag, userId as string);
      userFlags[flag.name] = isEnabled;
    }

    // Get transition phase for user
    const transitionPhase = determineTransitionPhase(userId as string);

    return res.status(200).json({
      success: true,
      message: 'Feature flag status retrieved successfully',
      data: {
        userId,
        environment,
        flags: userFlags,
        transitionPhase,
        evaluatedAt: new Date(),
        totalFlags: activeFlags.length,
        enabledFlags: Object.values(userFlags).filter(Boolean).length,
      },
    });
  } catch (error) {
    console.error('❌ Failed to get feature flag status:', error);
    
    return res.status(500).json({
      success: false,
      message: 'Internal server error while retrieving feature flag status',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

/**
 * Create A/B test
 */
export const handleCreateABTest = async (req: Request, res: Response, context: any) => {
  try {
    // Validate request body
    const parsedBody = abTestSchema.safeParse(req.body);
    if (!parsedBody.success) {
      return res.status(400).json({
        success: false,
        message: 'Invalid A/B test configuration',
        errors: parsedBody.error.format(),
      });
    }

    const testData = parsedBody.data;

    // Validate variant percentages sum to 100
    const totalPercentage = testData.variants.reduce((sum, variant) => sum + variant.percentage, 0);
    if (Math.abs(totalPercentage - 100) > 0.01) {
      return res.status(400).json({
        success: false,
        message: 'Variant percentages must sum to 100%',
        data: { totalPercentage, variants: testData.variants },
      });
    }

    const testId = `test_${Date.now()}`;
    const now = new Date();

    const abTest: ABTest = {
      id: testId,
      name: testData.name,
      description: testData.description,
      variants: testData.variants,
      targetAudience: testData.targetAudience,
      metrics: testData.metrics,
      duration: testData.duration,
      status: 'draft',
      createdAt: now,
    };

    abTests.set(testId, abTest);

    console.log(`🧪 A/B test created: ${abTest.name}`);

    return res.status(201).json({
      success: true,
      message: 'A/B test created successfully',
      data: abTest,
    });
  } catch (error) {
    console.error('❌ A/B test creation failed:', error);
    
    return res.status(500).json({
      success: false,
      message: 'Internal server error during A/B test creation',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

/**
 * Get A/B test assignment for user
 */
export const handleGetABTestAssignment = async (req: Request, res: Response, context: any) => {
  try {
    const { userId, testId } = req.query;

    if (!userId || !testId) {
      return res.status(400).json({
        success: false,
        message: 'User ID and test ID are required',
      });
    }

    const abTest = abTests.get(testId as string);
    if (!abTest) {
      return res.status(404).json({
        success: false,
        message: 'A/B test not found',
      });
    }

    if (abTest.status !== 'running') {
      return res.status(400).json({
        success: false,
        message: 'A/B test is not currently running',
        data: { status: abTest.status },
      });
    }

    // Assign user to variant based on consistent hashing
    const assignedVariant = assignUserToVariant(userId as string, abTest);

    return res.status(200).json({
      success: true,
      message: 'A/B test assignment retrieved successfully',
      data: {
        userId,
        testId: abTest.id,
        testName: abTest.name,
        assignedVariant,
        assignedAt: new Date(),
      },
    });
  } catch (error) {
    console.error('❌ Failed to get A/B test assignment:', error);
    
    return res.status(500).json({
      success: false,
      message: 'Internal server error while retrieving A/B test assignment',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

/**
 * Update transition configuration
 */
export const handleUpdateTransitionConfig = async (req: Request, res: Response, context: any) => {
  try {
    // Validate request body
    const parsedBody = transitionConfigSchema.safeParse(req.body);
    if (!parsedBody.success) {
      return res.status(400).json({
        success: false,
        message: 'Invalid transition configuration',
        errors: parsedBody.error.format(),
      });
    }

    const config = parsedBody.data;

    // Store transition configuration
    const transitionConfig = {
      ...config,
      updatedAt: new Date(),
      updatedBy: 'system', // In production, use authenticated user
    };

    // Update feature flags based on transition phase
    await updateFeatureFlagsForPhase(config.phase, config.rolloutPercentage);

    console.log(`🔄 Transition configuration updated to phase: ${config.phase}`);

    return res.status(200).json({
      success: true,
      message: 'Transition configuration updated successfully',
      data: transitionConfig,
    });
  } catch (error) {
    console.error('❌ Transition configuration update failed:', error);
    
    return res.status(500).json({
      success: false,
      message: 'Internal server error during transition configuration update',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

// Helper functions

function evaluateFeatureFlagForUser(flag: FeatureFlag, userId: string): boolean {
  // Check explicit inclusion/exclusion
  if (flag.excludeUsers?.includes(userId)) return false;
  if (flag.targetUsers?.includes(userId)) return true;

  // Check rollout percentage using consistent hashing
  const hash = hashUserId(userId);
  const userPercentile = hash % 100;
  
  return userPercentile < flag.rolloutPercentage;
}

function determineTransitionPhase(userId: string): string {
  // Determine which transition phase the user is in
  const hash = hashUserId(userId);
  const userPercentile = hash % 100;

  if (userPercentile < 10) return 'new_system';
  if (userPercentile < 50) return 'hybrid';
  return 'legacy_system';
}

function assignUserToVariant(userId: string, abTest: ABTest): any {
  const hash = hashUserId(userId);
  const userPercentile = hash % 100;

  let cumulativePercentage = 0;
  for (const variant of abTest.variants) {
    cumulativePercentage += variant.percentage;
    if (userPercentile < cumulativePercentage) {
      return variant;
    }
  }

  // Fallback to first variant
  return abTest.variants[0];
}

function hashUserId(userId: string): number {
  let hash = 0;
  for (let i = 0; i < userId.length; i++) {
    const char = userId.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return Math.abs(hash);
}

async function updateFeatureFlagsForPhase(phase: string, rolloutPercentage: number): Promise<void> {
  const phaseConfigs: Record<string, { flags: string[], percentage: number }> = {
    'planning': { flags: [], percentage: 0 },
    'pilot': { flags: ['new_subscription_ui', 'enhanced_checkout'], percentage: 5 },
    'gradual_rollout': { flags: ['new_subscription_ui', 'enhanced_checkout', 'dual_system'], percentage: rolloutPercentage },
    'full_migration': { flags: ['new_subscription_ui', 'enhanced_checkout', 'dual_system', 'migration_complete'], percentage: 100 },
    'cleanup': { flags: ['migration_complete'], percentage: 100 },
  };

  const config = phaseConfigs[phase];
  if (config) {
    // Update existing flags or create new ones
    for (const flagName of config.flags) {
      const existingFlag = Array.from(featureFlags.values()).find(f => f.name === flagName);
      if (existingFlag) {
        existingFlag.enabled = true;
        existingFlag.rolloutPercentage = config.percentage;
        existingFlag.updatedAt = new Date();
      }
    }
  }
}

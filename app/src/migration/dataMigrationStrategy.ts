import { type Request, type Response } from 'express';
import { HttpError } from 'wasp/server';
import { z } from 'zod';

/**
 * Comprehensive data migration strategy for UserSubscription model
 * Includes migration scripts, rollback procedures, and data validation
 */

// Migration configuration schema
const migrationConfigSchema = z.object({
  batchSize: z.number().min(1).max(1000).default(100),
  dryRun: z.boolean().default(true),
  validateOnly: z.boolean().default(false),
  rollback: z.boolean().default(false),
  targetUsers: z.array(z.string()).optional(),
});

interface MigrationResult {
  success: boolean;
  usersProcessed: number;
  subscriptionsCreated: number;
  errors: string[];
  warnings: string[];
  duration: number;
  timestamp: Date;
}

interface UserMigrationData {
  id: string;
  userId: string;
  email: string;
  legacySubscriptionStatus: string | null;
  legacySubscriptionPlan: string | null;
  credits: number;
  datePaid: Date | null;
  paymentProcessorUserId: string | null;
}

/**
 * Analyze existing data for migration planning
 */
export const handleAnalyzeMigrationData = async (req: Request, res: Response, context: any) => {
  try {
    console.log('📊 Starting migration data analysis...');

    // Get all users with legacy subscription data
    const usersWithLegacyData = await context.entities.User.findMany({
      where: {
        OR: [
          { subscriptionStatus: { not: null } },
          { subscriptionPlan: { not: null } },
          { paymentProcessorUserId: { not: null } },
        ],
      },
      select: {
        id: true,
        email: true,
        subscriptionStatus: true,
        subscriptionPlan: true,
        credits: true,
        datePaid: true,
        paymentProcessorUserId: true,
        createdAt: true,
      },
    });

    // Get existing UserSubscription records
    const existingUserSubscriptions = await context.entities.UserSubscription.findMany({
      select: {
        userId: true,
        status: true,
        subscriptionId: true,
      },
    });

    // Analyze subscription plans
    const planDistribution = usersWithLegacyData.reduce((acc: Record<string, number>, user) => {
      const plan = user.subscriptionPlan || 'none';
      acc[plan] = (acc[plan] || 0) + 1;
      return acc;
    }, {});

    // Analyze subscription statuses
    const statusDistribution = usersWithLegacyData.reduce((acc: Record<string, number>, user) => {
      const status = user.subscriptionStatus || 'none';
      acc[status] = (acc[status] || 0) + 1;
      return acc;
    }, {});

    // Find users already migrated
    const migratedUserIds = new Set(existingUserSubscriptions.map(sub => sub.userId));
    const usersToMigrate = usersWithLegacyData.filter(user => !migratedUserIds.has(user.id));

    // Calculate migration complexity
    const complexityFactors = {
      totalUsers: usersWithLegacyData.length,
      usersToMigrate: usersToMigrate.length,
      alreadyMigrated: migratedUserIds.size,
      uniquePlans: Object.keys(planDistribution).length,
      uniqueStatuses: Object.keys(statusDistribution).length,
      usersWithPaymentData: usersWithLegacyData.filter(u => u.paymentProcessorUserId).length,
      usersWithCredits: usersWithLegacyData.filter(u => u.credits > 0).length,
    };

    const analysis = {
      summary: {
        totalUsersWithLegacyData: usersWithLegacyData.length,
        usersAlreadyMigrated: migratedUserIds.size,
        usersToMigrate: usersToMigrate.length,
        migrationProgress: `${Math.round((migratedUserIds.size / usersWithLegacyData.length) * 100)}%`,
      },
      distributions: {
        subscriptionPlans: planDistribution,
        subscriptionStatuses: statusDistribution,
      },
      complexity: complexityFactors,
      recommendations: generateMigrationRecommendations(complexityFactors),
      sampleUsers: usersToMigrate.slice(0, 5).map(user => ({
        id: user.id,
        email: user.email,
        plan: user.subscriptionPlan,
        status: user.subscriptionStatus,
        credits: user.credits,
        hasPaymentData: !!user.paymentProcessorUserId,
      })),
    };

    return res.status(200).json({
      success: true,
      message: 'Migration data analysis completed',
      data: analysis,
    });
  } catch (error) {
    console.error('❌ Migration data analysis failed:', error);
    
    return res.status(500).json({
      success: false,
      message: 'Internal server error during migration analysis',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

/**
 * Execute data migration
 */
export const handleExecuteMigration = async (req: Request, res: Response, context: any) => {
  try {
    // Validate migration configuration
    const parsedConfig = migrationConfigSchema.safeParse(req.body);
    if (!parsedConfig.success) {
      return res.status(400).json({
        success: false,
        message: 'Invalid migration configuration',
        errors: parsedConfig.error.format(),
      });
    }

    const config = parsedConfig.data;
    const startTime = Date.now();

    console.log(`🚀 Starting migration with config:`, config);

    if (config.rollback) {
      return await executeRollback(config, context, res);
    }

    if (config.validateOnly) {
      return await validateMigrationData(config, context, res);
    }

    // Get users to migrate
    let usersToMigrate: UserMigrationData[];
    
    if (config.targetUsers && config.targetUsers.length > 0) {
      // Migrate specific users
      usersToMigrate = await context.entities.User.findMany({
        where: {
          id: { in: config.targetUsers },
        },
        select: {
          id: true,
          email: true,
          subscriptionStatus: true,
          subscriptionPlan: true,
          credits: true,
          datePaid: true,
          paymentProcessorUserId: true,
        },
      });
    } else {
      // Migrate all users with legacy data who haven't been migrated yet
      const existingUserSubscriptions = await context.entities.UserSubscription.findMany({
        select: { userId: true },
      });
      const migratedUserIds = new Set(existingUserSubscriptions.map(sub => sub.userId));

      usersToMigrate = await context.entities.User.findMany({
        where: {
          AND: [
            {
              OR: [
                { subscriptionStatus: { not: null } },
                { subscriptionPlan: { not: null } },
                { paymentProcessorUserId: { not: null } },
              ],
            },
            { id: { notIn: Array.from(migratedUserIds) } },
          ],
        },
        select: {
          id: true,
          email: true,
          subscriptionStatus: true,
          subscriptionPlan: true,
          credits: true,
          datePaid: true,
          paymentProcessorUserId: true,
        },
        take: config.batchSize,
      });
    }

    const migrationResult: MigrationResult = {
      success: true,
      usersProcessed: 0,
      subscriptionsCreated: 0,
      errors: [],
      warnings: [],
      duration: 0,
      timestamp: new Date(),
    };

    // Process users in batches
    for (const user of usersToMigrate) {
      try {
        const migrationData = await migrateUserSubscription(user, context, config.dryRun);
        
        migrationResult.usersProcessed++;
        if (migrationData.subscriptionCreated) {
          migrationResult.subscriptionsCreated++;
        }
        
        if (migrationData.warnings.length > 0) {
          migrationResult.warnings.push(...migrationData.warnings);
        }
      } catch (error) {
        const errorMessage = `Failed to migrate user ${user.id}: ${error instanceof Error ? error.message : 'Unknown error'}`;
        migrationResult.errors.push(errorMessage);
        console.error('❌', errorMessage);
      }
    }

    migrationResult.duration = Date.now() - startTime;
    migrationResult.success = migrationResult.errors.length === 0;

    return res.status(200).json({
      success: true,
      message: config.dryRun ? 'Migration dry run completed' : 'Migration executed successfully',
      data: migrationResult,
    });
  } catch (error) {
    console.error('❌ Migration execution failed:', error);
    
    return res.status(500).json({
      success: false,
      message: 'Internal server error during migration execution',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

/**
 * Migrate individual user subscription
 */
async function migrateUserSubscription(
  user: UserMigrationData,
  context: any,
  dryRun: boolean
): Promise<{ subscriptionCreated: boolean; warnings: string[] }> {
  const warnings: string[] = [];

  // Determine subscription mapping
  const subscriptionMapping = await mapLegacyPlanToSubscription(user.legacySubscriptionPlan, context);
  
  if (!subscriptionMapping) {
    warnings.push(`No subscription mapping found for plan: ${user.legacySubscriptionPlan}`);
    return { subscriptionCreated: false, warnings };
  }

  // Determine subscription status
  const status = mapLegacyStatusToNewStatus(user.legacySubscriptionStatus);
  
  // Calculate dates
  const startDate = user.datePaid || new Date();
  const endDate = calculateSubscriptionEndDate(startDate, subscriptionMapping.duration);

  if (dryRun) {
    console.log(`🔍 DRY RUN: Would create UserSubscription for user ${user.userId} with subscription ${subscriptionMapping.id}`);
    return { subscriptionCreated: true, warnings };
  }

  // Create UserSubscription record
  await context.entities.UserSubscription.create({
    data: {
      userId: user.userId,
      subscriptionId: subscriptionMapping.id,
      status,
      startDate,
      endDate,
      creditsAllocated: subscriptionMapping.creditsIncluded,
      paymentProcessorSubscriptionId: user.paymentProcessorUserId,
    },
  });

  console.log(`✅ Created UserSubscription for user ${user.userId}`);
  return { subscriptionCreated: true, warnings };
}

/**
 * Map legacy plan to new subscription
 */
async function mapLegacyPlanToSubscription(legacyPlan: string | null, context: any): Promise<any> {
  if (!legacyPlan) return null;

  const planMapping: Record<string, string> = {
    'hobby': 'Hobby Plan',
    'pro': 'Pro Plan',
    'free': 'Free Plan',
  };

  const subscriptionName = planMapping[legacyPlan.toLowerCase()];
  if (!subscriptionName) return null;

  return await context.entities.Subscription.findFirst({
    where: { 
      name: { contains: subscriptionName, mode: 'insensitive' },
      isActive: true,
    },
  });
}

/**
 * Map legacy status to new status
 */
function mapLegacyStatusToNewStatus(legacyStatus: string | null): string {
  const statusMapping: Record<string, string> = {
    'active': 'active',
    'cancel_at_period_end': 'active',
    'past_due': 'expired',
    'deleted': 'cancelled',
    'cancelled': 'cancelled',
  };

  return statusMapping[legacyStatus || ''] || 'pending';
}

/**
 * Calculate subscription end date
 */
function calculateSubscriptionEndDate(startDate: Date, duration: number): Date {
  return new Date(startDate.getTime() + duration * 24 * 60 * 60 * 1000);
}

/**
 * Generate migration recommendations
 */
function generateMigrationRecommendations(complexity: any): string[] {
  const recommendations: string[] = [];

  if (complexity.usersToMigrate > 1000) {
    recommendations.push('Consider migrating in smaller batches (100-500 users) to avoid performance issues');
  }

  if (complexity.uniquePlans > 5) {
    recommendations.push('Review subscription plan mappings before migration');
  }

  if (complexity.usersWithPaymentData > 0) {
    recommendations.push('Verify payment processor integration before migrating users with payment data');
  }

  if (complexity.alreadyMigrated > 0) {
    recommendations.push('Some users are already migrated. Use targetUsers to avoid duplicates');
  }

  recommendations.push('Always run with dryRun: true first to validate the migration');
  recommendations.push('Create database backup before executing migration');

  return recommendations;
}

/**
 * Execute rollback procedure
 */
async function executeRollback(config: any, context: any, res: Response): Promise<Response> {
  console.log('🔄 Starting migration rollback...');
  
  // This is a simplified rollback - in production, you'd want more sophisticated rollback logic
  const deletedCount = await context.entities.UserSubscription.deleteMany({
    where: {
      // Add conditions to identify migrated records
      createdAt: {
        gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
      },
    },
  });

  return res.status(200).json({
    success: true,
    message: 'Rollback completed',
    data: {
      deletedSubscriptions: deletedCount.count,
      timestamp: new Date(),
    },
  });
}

/**
 * Validate migration data
 */
async function validateMigrationData(config: any, context: any, res: Response): Promise<Response> {
  console.log('✅ Starting migration data validation...');
  
  const validationResults = {
    validUsers: 0,
    invalidUsers: 0,
    issues: [] as string[],
  };

  // Add validation logic here
  
  return res.status(200).json({
    success: true,
    message: 'Migration data validation completed',
    data: validationResults,
  });
}

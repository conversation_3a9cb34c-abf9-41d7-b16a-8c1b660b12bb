import { type Request, type Response } from 'express';
import { HttpError } from 'wasp/server';
import crypto from 'crypto';
import { z } from 'zod';

/**
 * Enhanced webhook endpoints for external website integration
 * Supports callback URLs, proper error handling, and external website notifications
 */

// Validation schemas
const callbackConfigSchema = z.object({
  url: z.string().url(),
  secret: z.string().min(10),
  events: z.array(z.string()).default(['*']),
  retryAttempts: z.number().min(1).max(5).default(3),
  timeoutMs: z.number().min(1000).max(30000).default(10000),
});

const webhookEventSchema = z.object({
  eventType: z.string(),
  eventId: z.string(),
  timestamp: z.string().datetime(),
  data: z.record(z.any()),
  metadata: z.record(z.string()).optional(),
});

interface CallbackConfig {
  url: string;
  secret: string;
  events: string[];
  retryAttempts: number;
  timeoutMs: number;
}

interface WebhookEvent {
  eventType: string;
  eventId: string;
  timestamp: string;
  data: Record<string, any>;
  metadata?: Record<string, string>;
}

// In-memory storage for callback configurations (in production, use database)
const callbackConfigurations = new Map<string, CallbackConfig[]>();

/**
 * Register callback URL for webhook events
 */
export const handleRegisterWebhookCallback = async (req: Request, res: Response, context: any) => {
  try {
    // Validate request body
    const parsedBody = callbackConfigSchema.safeParse(req.body);
    if (!parsedBody.success) {
      return res.status(400).json({
        success: false,
        message: 'Invalid callback configuration',
        errors: parsedBody.error.format(),
      });
    }

    const callbackConfig = parsedBody.data;
    const { clientId = 'default' } = req.query;

    // Store callback configuration
    if (!callbackConfigurations.has(clientId as string)) {
      callbackConfigurations.set(clientId as string, []);
    }

    const clientCallbacks = callbackConfigurations.get(clientId as string)!;
    
    // Check if URL already exists for this client
    const existingIndex = clientCallbacks.findIndex(cb => cb.url === callbackConfig.url);
    if (existingIndex >= 0) {
      // Update existing callback
      clientCallbacks[existingIndex] = callbackConfig;
    } else {
      // Add new callback
      clientCallbacks.push(callbackConfig);
    }

    return res.status(201).json({
      success: true,
      message: 'Webhook callback registered successfully',
      data: {
        clientId,
        callbackUrl: callbackConfig.url,
        events: callbackConfig.events,
        retryAttempts: callbackConfig.retryAttempts,
        timeoutMs: callbackConfig.timeoutMs,
        registeredAt: new Date(),
      },
    });
  } catch (error) {
    console.error('❌ Failed to register webhook callback:', error);
    
    return res.status(500).json({
      success: false,
      message: 'Internal server error while registering webhook callback',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

/**
 * Get registered callback URLs for a client
 */
export const handleGetWebhookCallbacks = async (req: Request, res: Response, context: any) => {
  try {
    const { clientId = 'default' } = req.query;

    const clientCallbacks = callbackConfigurations.get(clientId as string) || [];

    return res.status(200).json({
      success: true,
      message: 'Webhook callbacks retrieved successfully',
      data: {
        clientId,
        callbacks: clientCallbacks.map(cb => ({
          url: cb.url,
          events: cb.events,
          retryAttempts: cb.retryAttempts,
          timeoutMs: cb.timeoutMs,
        })),
        total: clientCallbacks.length,
      },
    });
  } catch (error) {
    console.error('❌ Failed to get webhook callbacks:', error);
    
    return res.status(500).json({
      success: false,
      message: 'Internal server error while retrieving webhook callbacks',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

/**
 * Remove webhook callback
 */
export const handleRemoveWebhookCallback = async (req: Request, res: Response, context: any) => {
  try {
    const { clientId = 'default', callbackUrl } = req.query;

    if (!callbackUrl) {
      return res.status(400).json({
        success: false,
        message: 'Callback URL is required',
      });
    }

    const clientCallbacks = callbackConfigurations.get(clientId as string) || [];
    const initialLength = clientCallbacks.length;

    // Remove callback with matching URL
    const updatedCallbacks = clientCallbacks.filter(cb => cb.url !== callbackUrl);
    callbackConfigurations.set(clientId as string, updatedCallbacks);

    const removed = initialLength > updatedCallbacks.length;

    return res.status(200).json({
      success: true,
      message: removed ? 'Webhook callback removed successfully' : 'Callback URL not found',
      data: {
        clientId,
        callbackUrl,
        removed,
        remainingCallbacks: updatedCallbacks.length,
      },
    });
  } catch (error) {
    console.error('❌ Failed to remove webhook callback:', error);
    
    return res.status(500).json({
      success: false,
      message: 'Internal server error while removing webhook callback',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

/**
 * Send webhook event to registered callbacks
 */
async function sendWebhookEvent(event: WebhookEvent, clientId: string = 'default'): Promise<void> {
  const clientCallbacks = callbackConfigurations.get(clientId) || [];

  for (const callback of clientCallbacks) {
    // Check if this callback is interested in this event type
    if (!callback.events.includes('*') && !callback.events.includes(event.eventType)) {
      continue;
    }

    // Send webhook with retry logic
    await sendWebhookWithRetry(event, callback);
  }
}

/**
 * Send webhook with retry logic
 */
async function sendWebhookWithRetry(event: WebhookEvent, callback: CallbackConfig): Promise<void> {
  for (let attempt = 1; attempt <= callback.retryAttempts; attempt++) {
    try {
      await sendSingleWebhook(event, callback);
      console.log(`✅ Webhook sent successfully to ${callback.url} (attempt ${attempt})`);
      return; // Success, no need to retry
    } catch (error) {
      console.warn(`❌ Webhook attempt ${attempt} failed for ${callback.url}:`, error);
      
      if (attempt === callback.retryAttempts) {
        console.error(`🚨 All webhook attempts failed for ${callback.url}`);
      } else {
        // Wait before retry (exponential backoff)
        const delay = Math.min(1000 * Math.pow(2, attempt - 1), 10000);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }
}

/**
 * Send single webhook request
 */
async function sendSingleWebhook(event: WebhookEvent, callback: CallbackConfig): Promise<void> {
  const payload = JSON.stringify(event);
  
  // Create signature for webhook verification
  const signature = crypto
    .createHmac('sha256', callback.secret)
    .update(payload)
    .digest('hex');

  const response = await fetch(callback.url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-Webhook-Signature': `sha256=${signature}`,
      'X-Webhook-Event': event.eventType,
      'X-Webhook-Id': event.eventId,
      'User-Agent': 'Dalti-Webhooks/1.0',
    },
    body: payload,
    signal: AbortSignal.timeout(callback.timeoutMs),
  });

  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
  }
}

/**
 * Enhanced webhook handler for subscription events
 */
export const handleEnhancedSubscriptionWebhook = async (req: Request, res: Response, context: any) => {
  try {
    // Validate webhook event
    const parsedBody = webhookEventSchema.safeParse(req.body);
    if (!parsedBody.success) {
      return res.status(400).json({
        success: false,
        message: 'Invalid webhook event format',
        errors: parsedBody.error.format(),
      });
    }

    const webhookEvent = parsedBody.data;
    const { clientId = 'default' } = req.query;

    console.log(`📨 Received enhanced webhook event: ${webhookEvent.eventType} for client: ${clientId}`);

    // Process the webhook event based on type
    let processedData: any = {};

    switch (webhookEvent.eventType) {
      case 'subscription.created':
        processedData = await processSubscriptionCreated(webhookEvent.data, context);
        break;
      case 'subscription.updated':
        processedData = await processSubscriptionUpdated(webhookEvent.data, context);
        break;
      case 'subscription.cancelled':
        processedData = await processSubscriptionCancelled(webhookEvent.data, context);
        break;
      case 'subscription.expired':
        processedData = await processSubscriptionExpired(webhookEvent.data, context);
        break;
      case 'payment.succeeded':
        processedData = await processPaymentSucceeded(webhookEvent.data, context);
        break;
      case 'payment.failed':
        processedData = await processPaymentFailed(webhookEvent.data, context);
        break;
      default:
        console.warn(`⚠️ Unknown webhook event type: ${webhookEvent.eventType}`);
        processedData = { message: 'Event type not handled' };
    }

    // Send webhook to registered callbacks
    const enhancedEvent: WebhookEvent = {
      ...webhookEvent,
      data: {
        ...webhookEvent.data,
        processed: processedData,
      },
      metadata: {
        ...webhookEvent.metadata,
        processedAt: new Date().toISOString(),
        clientId: clientId as string,
      },
    };

    // Send to external callbacks (non-blocking)
    sendWebhookEvent(enhancedEvent, clientId as string).catch(error => {
      console.error('❌ Failed to send webhook to callbacks:', error);
    });

    return res.status(200).json({
      success: true,
      message: 'Enhanced webhook processed successfully',
      data: {
        eventType: webhookEvent.eventType,
        eventId: webhookEvent.eventId,
        processed: processedData,
        callbacksSent: (callbackConfigurations.get(clientId as string) || []).length,
        timestamp: new Date(),
      },
    });
  } catch (error) {
    console.error('❌ Enhanced webhook processing failed:', error);
    
    if (error instanceof HttpError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message,
      });
    }

    return res.status(500).json({
      success: false,
      message: 'Internal server error during enhanced webhook processing',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

// Helper functions for processing different event types
async function processSubscriptionCreated(data: any, context: any): Promise<any> {
  console.log('🆕 Processing subscription created event');
  return { action: 'subscription_created', userId: data.userId, subscriptionId: data.subscriptionId };
}

async function processSubscriptionUpdated(data: any, context: any): Promise<any> {
  console.log('🔄 Processing subscription updated event');
  return { action: 'subscription_updated', userId: data.userId, changes: data.changes };
}

async function processSubscriptionCancelled(data: any, context: any): Promise<any> {
  console.log('❌ Processing subscription cancelled event');
  return { action: 'subscription_cancelled', userId: data.userId, reason: data.reason };
}

async function processSubscriptionExpired(data: any, context: any): Promise<any> {
  console.log('⏰ Processing subscription expired event');
  return { action: 'subscription_expired', userId: data.userId, expiredAt: data.expiredAt };
}

async function processPaymentSucceeded(data: any, context: any): Promise<any> {
  console.log('💰 Processing payment succeeded event');
  return { action: 'payment_succeeded', userId: data.userId, amount: data.amount };
}

async function processPaymentFailed(data: any, context: any): Promise<any> {
  console.log('💸 Processing payment failed event');
  return { action: 'payment_failed', userId: data.userId, reason: data.reason };
}

#!/usr/bin/env node

/**
 * Migration script to initialize subscription system
 * Run this script to migrate from legacy PaymentPlanId system to new Subscription model
 * 
 * Usage:
 * npx ts-node src/payment/runMigration.ts [command]
 * 
 * Commands:
 * - init: Initialize subscription records from payment plans
 * - migrate: Migrate user subscriptions to new model
 * - validate: Validate migration integrity
 * - rollback: Rollback migration (emergency use only)
 * - full: Run complete migration (init + migrate + validate)
 */

import { PrismaClient } from '@prisma/client';
import { createPaymentSystemMigration } from './migrationUtils';

const prisma = new PrismaClient();

async function main() {
  const command = process.argv[2] || 'full';
  const migration = createPaymentSystemMigration(prisma);

  console.log(`🚀 Starting payment system migration: ${command}`);
  console.log('=====================================');

  try {
    switch (command) {
      case 'init':
        await migration.initializeSubscriptionRecords();
        break;

      case 'migrate':
        await migration.migrateUserSubscriptions();
        break;

      case 'validate':
        {
          const isValid = await migration.validateMigration();
          process.exit(isValid ? 0 : 1);
        }
        break;

      case 'rollback':
        console.log('⚠️  WARNING: This will delete all migrated data!');
        console.log('⚠️  Make sure you have a database backup before proceeding.');

        // In a real scenario, you might want to add a confirmation prompt
        await migration.rollbackMigration();
        break;

      case 'full':
        {
          console.log('📋 Running complete migration process...');

          // Step 1: Initialize subscription records
          console.log('\n1️⃣ Initializing subscription records...');
          await migration.initializeSubscriptionRecords();

          // Step 2: Migrate user subscriptions
          console.log('\n2️⃣ Migrating user subscriptions...');
          await migration.migrateUserSubscriptions();

          // Step 3: Validate migration
          console.log('\n3️⃣ Validating migration...');
          const isValid = await migration.validateMigration();

          if (isValid) {
            console.log('\n✅ Complete migration finished successfully!');
          } else {
            console.log('\n❌ Migration completed with validation errors');
            process.exit(1);
          }
        }
        break;

      default:
        console.error(`❌ Unknown command: ${command}`);
        console.log('\nAvailable commands:');
        console.log('  init     - Initialize subscription records');
        console.log('  migrate  - Migrate user subscriptions');
        console.log('  validate - Validate migration integrity');
        console.log('  rollback - Rollback migration (emergency)');
        console.log('  full     - Run complete migration');
        process.exit(1);
    }

    console.log('\n✅ Migration command completed successfully');
  } catch (error) {
    console.error('\n❌ Migration failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (error) => {
  console.error('Unhandled promise rejection:', error);
  process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught exception:', error);
  process.exit(1);
});

// Run the migration
main().catch((error) => {
  console.error('Migration script failed:', error);
  process.exit(1);
});

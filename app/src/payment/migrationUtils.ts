import { PrismaClient } from '@prisma/client';
import { createSubscriptionMappingService } from './subscriptionMapping';
import { PaymentPlanId, paymentPlans } from './plans';

/**
 * Migration utilities for transitioning from legacy PaymentPlanId system
 * to new Subscription model system
 */

export class PaymentSystemMigration {
  private prisma: PrismaClient;
  private mappingService: any;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
    this.mappingService = createSubscriptionMappingService(prisma);
  }

  /**
   * Initialize subscription records from existing payment plans
   * This should be run once during the migration process
   */
  async initializeSubscriptionRecords(): Promise<void> {
    console.log('🚀 Starting subscription records initialization...');
    
    try {
      await this.mappingService.createSubscriptionsFromPaymentPlans();
      console.log('✅ Subscription records initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize subscription records:', error);
      throw error;
    }
  }

  /**
   * Migrate existing user subscription data to UserSubscription model
   * This will create UserSubscription records for users who have active subscriptions
   */
  async migrateUserSubscriptions(): Promise<void> {
    console.log('🚀 Starting user subscription migration...');
    
    try {
      // Get all users with active subscriptions
      const usersWithSubscriptions = await this.prisma.user.findMany({
        where: {
          AND: [
            { subscriptionStatus: { not: null } },
            { subscriptionPlan: { not: null } },
            { subscriptionStatus: { in: ['active', 'past_due'] } },
          ],
        },
        select: {
          id: true,
          subscriptionPlan: true,
          subscriptionStatus: true,
          datePaid: true,
          credits: true,
        },
      });

      console.log(`Found ${usersWithSubscriptions.length} users with active subscriptions`);

      let migratedCount = 0;
      let skippedCount = 0;

      for (const user of usersWithSubscriptions) {
        try {
          // Map legacy subscription plan to PaymentPlanId
          const paymentPlanId = this.mapLegacyPlanToPaymentPlanId(user.subscriptionPlan);
          
          if (!paymentPlanId) {
            console.warn(`Skipping user ${user.id}: Unknown subscription plan ${user.subscriptionPlan}`);
            skippedCount++;
            continue;
          }

          // Get corresponding Subscription record
          const subscription = await this.mappingService.getSubscriptionByPaymentPlanId(paymentPlanId);
          
          // Check if UserSubscription already exists
          const existingUserSubscription = await this.prisma.userSubscription.findFirst({
            where: {
              userId: user.id,
              subscriptionId: subscription.id,
              status: 'active',
            },
          });

          if (existingUserSubscription) {
            console.log(`UserSubscription already exists for user ${user.id}`);
            skippedCount++;
            continue;
          }

          // Calculate subscription dates
          const startDate = user.datePaid || new Date();
          const endDate = this.calculateEndDate(startDate, subscription.interval, subscription.duration);

          // Create UserSubscription record
          await this.prisma.userSubscription.create({
            data: {
              userId: user.id,
              subscriptionId: subscription.id,
              status: this.mapLegacyStatusToNewStatus(user.subscriptionStatus),
              startDate,
              endDate,
              creditsAllocated: subscription.creditsIncluded,
              paymentProcessorSubscriptionId: null, // Will be populated by webhooks
            },
          });

          migratedCount++;
          console.log(`✅ Migrated subscription for user ${user.id}`);
        } catch (error) {
          console.error(`❌ Failed to migrate subscription for user ${user.id}:`, error);
          skippedCount++;
        }
      }

      console.log(`✅ User subscription migration completed: ${migratedCount} migrated, ${skippedCount} skipped`);
    } catch (error) {
      console.error('❌ Failed to migrate user subscriptions:', error);
      throw error;
    }
  }

  /**
   * Validate migration integrity
   * Checks that all migrated data is consistent
   */
  async validateMigration(): Promise<boolean> {
    console.log('🔍 Validating migration integrity...');
    
    try {
      // Check that all PaymentPlanIds have corresponding Subscription records
      const paymentPlanIds = Object.values(PaymentPlanId);
      let validationErrors = 0;

      for (const planId of paymentPlanIds) {
        try {
          await this.mappingService.getSubscriptionByPaymentPlanId(planId);
          console.log(`✅ Subscription record exists for ${planId}`);
        } catch (error) {
          console.error(`❌ Missing subscription record for ${planId}:`, error);
          validationErrors++;
        }
      }

      // Check UserSubscription data consistency
      const userSubscriptions = await this.prisma.userSubscription.findMany({
        include: {
          user: true,
          subscription: true,
        },
      });

      for (const userSub of userSubscriptions) {
        // Validate that user still exists and has compatible data
        if (!userSub.user) {
          console.error(`❌ UserSubscription ${userSub.id} references non-existent user`);
          validationErrors++;
        }

        // Validate that subscription still exists
        if (!userSub.subscription) {
          console.error(`❌ UserSubscription ${userSub.id} references non-existent subscription`);
          validationErrors++;
        }
      }

      if (validationErrors === 0) {
        console.log('✅ Migration validation passed');
        return true;
      } else {
        console.error(`❌ Migration validation failed with ${validationErrors} errors`);
        return false;
      }
    } catch (error) {
      console.error('❌ Migration validation failed:', error);
      return false;
    }
  }

  /**
   * Rollback migration (emergency use only)
   * Removes all UserSubscription records created during migration
   */
  async rollbackMigration(): Promise<void> {
    console.log('⚠️  Starting migration rollback...');
    
    try {
      // Delete all UserSubscription records
      const deletedUserSubscriptions = await this.prisma.userSubscription.deleteMany({});
      console.log(`Deleted ${deletedUserSubscriptions.count} UserSubscription records`);

      // Optionally delete Subscription records created from PaymentPlans
      // (Only if they were created during migration and not manually)
      const deletedSubscriptions = await this.prisma.subscription.deleteMany({
        where: {
          paymentProcessorPlanId: { not: null },
        },
      });
      console.log(`Deleted ${deletedSubscriptions.count} Subscription records`);

      console.log('✅ Migration rollback completed');
    } catch (error) {
      console.error('❌ Migration rollback failed:', error);
      throw error;
    }
  }

  /**
   * Map legacy subscription plan names to PaymentPlanId
   */
  private mapLegacyPlanToPaymentPlanId(legacyPlan: string | null): PaymentPlanId | null {
    if (!legacyPlan) return null;

    const mapping: Record<string, PaymentPlanId> = {
      'free': PaymentPlanId.Free,
      'hobby': PaymentPlanId.Hobby,
      'pro': PaymentPlanId.Pro,
      'credits10': PaymentPlanId.Credits10,
      // Add any other legacy plan names that might exist
      'starter': PaymentPlanId.Hobby, // If 'starter' was used instead of 'hobby'
      'business': PaymentPlanId.Pro,  // If 'business' was used instead of 'pro'
    };

    return mapping[legacyPlan.toLowerCase()] || null;
  }

  /**
   * Map legacy subscription status to new status
   */
  private mapLegacyStatusToNewStatus(legacyStatus: string | null): string {
    if (!legacyStatus) return 'pending';

    const mapping: Record<string, string> = {
      'active': 'active',
      'past_due': 'active', // Keep as active but monitor
      'cancel_at_period_end': 'active', // Still active until period ends
      'deleted': 'cancelled',
      'cancelled': 'cancelled',
      'expired': 'expired',
    };

    return mapping[legacyStatus] || 'pending';
  }

  /**
   * Calculate subscription end date based on interval and duration
   */
  private calculateEndDate(startDate: Date, interval: string, duration: number): Date | null {
    if (interval === 'one-time') {
      return null; // One-time purchases don't expire
    }

    const endDate = new Date(startDate);
    
    if (interval === 'monthly') {
      endDate.setMonth(endDate.getMonth() + 1);
    } else if (interval === 'yearly') {
      endDate.setFullYear(endDate.getFullYear() + 1);
    } else {
      // Use duration in days as fallback
      endDate.setDate(endDate.getDate() + duration);
    }

    return endDate;
  }
}

/**
 * Helper function to create migration instance
 */
export function createPaymentSystemMigration(prisma: PrismaClient): PaymentSystemMigration {
  return new PaymentSystemMigration(prisma);
}

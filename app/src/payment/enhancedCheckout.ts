import * as z from 'zod';
import { HttpError } from 'wasp/server';
import { PaymentPlanId, paymentPlans } from './plans';
import { paymentProcessor } from './paymentProcessor';
import { createSubscriptionMappingService, isPaymentPlanId, isSubscriptionId } from './subscriptionMapping';
import { ensureArgsSchemaOrThrowHttpError } from '../server/validation';

/**
 * Enhanced checkout service that supports both legacy PaymentPlanId and new Subscription IDs
 * Provides seamless operation during transition period
 */

export type EnhancedCheckoutSession = {
  sessionUrl: string | null;
  sessionId: string;
  planType: 'legacy' | 'subscription';
  planIdentifier: string;
  subscriptionId?: string;
  paymentPlanId?: PaymentPlanId;
};

// Enhanced schema that accepts either PaymentPlanId or Subscription UUID
const enhancedCheckoutSessionSchema = z.union([
  z.nativeEnum(PaymentPlanId),
  z.string().uuid(),
]);

// Schema for subscription-based checkout with additional options
const subscriptionCheckoutSchema = z.object({
  subscriptionId: z.string().uuid(),
  returnUrl: z.string().url().optional(),
  cancelUrl: z.string().url().optional(),
  metadata: z.record(z.string()).optional(),
});

export type SubscriptionCheckoutInput = z.infer<typeof subscriptionCheckoutSchema>;

/**
 * Enhanced checkout session generation that supports both systems
 */
export async function generateEnhancedCheckoutSession(
  planIdentifier: string,
  context: any
): Promise<EnhancedCheckoutSession> {
  if (!context.user) {
    throw new HttpError(401, 'User authentication required');
  }

  const userId = context.user.id;
  const userEmail = context.user.email;
  
  if (!userEmail) {
    throw new HttpError(403, 'User needs an email to make a payment.');
  }

  // Validate the plan identifier
  const validatedIdentifier = ensureArgsSchemaOrThrowHttpError(enhancedCheckoutSessionSchema, planIdentifier);
  
  let paymentPlan: any;
  let planType: 'legacy' | 'subscription';
  let subscriptionId: string | undefined;
  let paymentPlanId: PaymentPlanId | undefined;

  if (isPaymentPlanId(validatedIdentifier)) {
    // Legacy PaymentPlanId flow
    planType = 'legacy';
    paymentPlanId = validatedIdentifier as PaymentPlanId;
    paymentPlan = paymentPlans[paymentPlanId];
    
    // Try to get corresponding subscription ID for tracking
    try {
      const mappingService = createSubscriptionMappingService(context.entities);
      const subscription = await mappingService.getSubscriptionByPaymentPlanId(paymentPlanId);
      subscriptionId = subscription.id;
    } catch (error) {
      console.warn(`Could not find subscription for PaymentPlanId ${paymentPlanId}:`, error);
    }
  } else if (isSubscriptionId(validatedIdentifier)) {
    // New Subscription ID flow
    planType = 'subscription';
    subscriptionId = validatedIdentifier;
    
    // Get subscription details and convert to PaymentPlan format
    const subscription = await context.entities.Subscription.findUnique({
      where: { id: subscriptionId },
    });

    if (!subscription || !subscription.isActive) {
      throw new HttpError(404, `Active subscription not found: ${subscriptionId}`);
    }

    if (!subscription.paymentProcessorPlanId) {
      throw new HttpError(400, `Subscription ${subscriptionId} is missing payment processor plan ID`);
    }

    // Convert Subscription to PaymentPlan format
    paymentPlan = {
      getPaymentProcessorPlanId: () => subscription.paymentProcessorPlanId,
      effect: {
        type: subscription.interval === 'one-time' ? 'one-time' : 'recurrent',
        kind: subscription.interval === 'one-time' ? 'credits' : 'subscription',
        amount: subscription.creditsIncluded,
        queues: subscription.interval === 'one-time' ? null : 
                subscription.creditsIncluded <= 50 ? 1 :
                subscription.creditsIncluded <= 200 ? 3 : 10,
      },
    };

    // Try to get corresponding PaymentPlanId for backward compatibility
    try {
      const mappingService = createSubscriptionMappingService(context.entities);
      paymentPlanId = await mappingService.getPaymentPlanIdBySubscriptionId(subscriptionId);
    } catch (error) {
      console.warn(`Could not find PaymentPlanId for subscription ${subscriptionId}:`, error);
    }
  } else {
    throw new HttpError(400, `Invalid plan identifier: ${validatedIdentifier}`);
  }

  // Generate checkout session using the payment processor
  const { session } = await paymentProcessor.createCheckoutSession({
    userId,
    userEmail,
    paymentPlan,
    prismaUserDelegate: context.entities.User,
  });

  return {
    sessionUrl: session.url,
    sessionId: session.id,
    planType,
    planIdentifier: validatedIdentifier,
    subscriptionId,
    paymentPlanId,
  };
}

/**
 * Subscription-specific checkout with additional options
 */
export async function generateSubscriptionCheckout(
  input: SubscriptionCheckoutInput,
  context: any
): Promise<EnhancedCheckoutSession> {
  if (!context.user) {
    throw new HttpError(401, 'User authentication required');
  }

  const { subscriptionId, returnUrl, cancelUrl, metadata } = input;
  
  // Validate subscription exists and is active
  const subscription = await context.entities.Subscription.findUnique({
    where: { id: subscriptionId },
  });

  if (!subscription || !subscription.isActive) {
    throw new HttpError(404, `Active subscription not found: ${subscriptionId}`);
  }

  // Generate enhanced checkout session
  const checkoutSession = await generateEnhancedCheckoutSession(subscriptionId, context);

  // Store additional metadata if provided
  if (returnUrl || cancelUrl || metadata) {
    // In a real implementation, you might want to store this in a CheckoutSession table
    // For now, we'll include it in the response
    console.log('Checkout metadata:', { returnUrl, cancelUrl, metadata });
  }

  return checkoutSession;
}

/**
 * Get checkout session status and details
 */
export async function getCheckoutSessionStatus(
  sessionId: string,
  context: any
): Promise<{
  sessionId: string;
  status: string;
  planIdentifier?: string;
  subscriptionId?: string;
  paymentPlanId?: string;
}> {
  // In a real implementation, you would query the payment processor for session status
  // For now, we'll return a mock response
  return {
    sessionId,
    status: 'pending', // Could be: pending, completed, expired, cancelled
    planIdentifier: 'unknown',
  };
}

/**
 * Validate checkout session data
 */
export function validateCheckoutInput(input: any): {
  isValid: boolean;
  planType: 'legacy' | 'subscription' | 'unknown';
  identifier: string | null;
  errors: string[];
} {
  const errors: string[] = [];
  let planType: 'legacy' | 'subscription' | 'unknown' = 'unknown';
  let identifier: string | null = null;

  if (!input) {
    errors.push('Input is required');
    return { isValid: false, planType, identifier, errors };
  }

  if (typeof input === 'string') {
    identifier = input;
    if (isPaymentPlanId(input)) {
      planType = 'legacy';
    } else if (isSubscriptionId(input)) {
      planType = 'subscription';
    } else {
      errors.push('Invalid plan identifier format');
    }
  } else if (typeof input === 'object' && input.subscriptionId) {
    identifier = input.subscriptionId;
    if (isSubscriptionId(input.subscriptionId)) {
      planType = 'subscription';
    } else {
      errors.push('Invalid subscription ID format');
    }
  } else {
    errors.push('Input must be a plan identifier string or subscription object');
  }

  return {
    isValid: errors.length === 0,
    planType,
    identifier,
    errors,
  };
}

// Export schemas for use in API handlers
export { enhancedCheckoutSessionSchema, subscriptionCheckoutSchema };

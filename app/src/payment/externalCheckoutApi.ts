import { type Request, type Response } from 'express';
import { HttpError } from 'wasp/server';
import { z } from 'zod';
import {
  generateEnhancedCheckoutSession,
  generateSubscriptionCheckout,
  getCheckoutSessionStatus,
  validateCheckoutInput,
  type SubscriptionCheckoutInput,
} from './enhancedCheckout';
import { createSubscriptionMappingService } from './subscriptionMapping';

/**
 * External API endpoints for subscription-based checkout
 * Designed for external website integration with comprehensive features
 */

// Enhanced validation schemas for external API
const externalCheckoutSchema = z.object({
  planIdentifier: z.string().min(1, 'Plan identifier is required'),
  returnUrl: z.string().url('Valid return URL is required'),
  cancelUrl: z.string().url('Valid cancel URL is required'),
  metadata: z.record(z.string()).optional(),
  customerInfo: z.object({
    email: z.string().email().optional(),
    name: z.string().optional(),
    phone: z.string().optional(),
  }).optional(),
});

const subscriptionCheckoutSchema = z.object({
  subscriptionId: z.string().uuid('Valid subscription ID is required'),
  returnUrl: z.string().url('Valid return URL is required'),
  cancelUrl: z.string().url('Valid cancel URL is required'),
  metadata: z.record(z.string()).optional(),
  customerInfo: z.object({
    email: z.string().email().optional(),
    name: z.string().optional(),
    phone: z.string().optional(),
  }).optional(),
});

const sessionStatusSchema = z.object({
  sessionId: z.string().min(1, 'Session ID is required'),
});

/**
 * Create checkout session for external websites
 * Supports both legacy PaymentPlanId and new Subscription IDs
 */
export const handleCreateExternalCheckout = async (req: Request, res: Response, context: any) => {
  try {
    // Validate request body
    const parsedBody = externalCheckoutSchema.safeParse(req.body);
    if (!parsedBody.success) {
      return res.status(400).json({
        success: false,
        message: 'Invalid request body',
        errors: parsedBody.error.format(),
      });
    }

    const { planIdentifier, returnUrl, cancelUrl, metadata, customerInfo } = parsedBody.data;

    // Validate plan identifier
    const validation = validateCheckoutInput(planIdentifier);
    if (!validation.isValid) {
      return res.status(400).json({
        success: false,
        message: 'Invalid plan identifier',
        errors: validation.errors,
        planType: validation.planType,
      });
    }

    // For external API, we need to handle user context differently
    // If no authenticated user, we'll create a temporary context
    let userContext = context.user;
    
    if (!userContext && customerInfo?.email) {
      // For external websites, we can create or find user by email
      userContext = await context.entities.User.findFirst({
        where: { email: customerInfo.email },
      });

      if (!userContext) {
        // Create a new user for external checkout
        userContext = await context.entities.User.create({
          data: {
            email: customerInfo.email,
            credits: 0,
            queues: 1,
          },
        });
      }
    }

    if (!userContext) {
      throw new HttpError(400, 'User context required. Provide customerInfo.email or authenticate user.');
    }

    // Create enhanced context for checkout
    const enhancedContext = {
      ...context,
      user: userContext,
    };

    // Generate checkout session
    const checkoutSession = await generateEnhancedCheckoutSession(planIdentifier, enhancedContext);

    // Store additional metadata for external integration
    const sessionData = {
      sessionId: checkoutSession.sessionId,
      sessionUrl: checkoutSession.sessionUrl,
      planType: checkoutSession.planType,
      planIdentifier: checkoutSession.planIdentifier,
      subscriptionId: checkoutSession.subscriptionId,
      paymentPlanId: checkoutSession.paymentPlanId,
      returnUrl,
      cancelUrl,
      metadata,
      customerInfo,
      userId: userContext.id,
      createdAt: new Date(),
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
    };

    return res.status(201).json({
      success: true,
      message: 'External checkout session created successfully',
      data: sessionData,
    });
  } catch (error) {
    console.error('❌ External checkout creation failed:', error);
    
    if (error instanceof HttpError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message,
      });
    }

    return res.status(500).json({
      success: false,
      message: 'Internal server error during external checkout creation',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

/**
 * Create subscription-specific checkout for external websites
 */
export const handleCreateExternalSubscriptionCheckout = async (req: Request, res: Response, context: any) => {
  try {
    // Validate request body
    const parsedBody = subscriptionCheckoutSchema.safeParse(req.body);
    if (!parsedBody.success) {
      return res.status(400).json({
        success: false,
        message: 'Invalid request body',
        errors: parsedBody.error.format(),
      });
    }

    const { subscriptionId, returnUrl, cancelUrl, metadata, customerInfo } = parsedBody.data;

    // Validate subscription exists
    const subscription = await context.entities.Subscription.findUnique({
      where: { id: subscriptionId },
    });

    if (!subscription || !subscription.isActive) {
      return res.status(404).json({
        success: false,
        message: 'Active subscription not found',
        subscriptionId,
      });
    }

    // Handle user context for external API
    let userContext = context.user;
    
    if (!userContext && customerInfo?.email) {
      userContext = await context.entities.User.findFirst({
        where: { email: customerInfo.email },
      });

      if (!userContext) {
        userContext = await context.entities.User.create({
          data: {
            email: customerInfo.email,
            credits: 0,
            queues: 1,
          },
        });
      }
    }

    if (!userContext) {
      throw new HttpError(400, 'User context required. Provide customerInfo.email or authenticate user.');
    }

    // Create enhanced context for checkout
    const enhancedContext = {
      ...context,
      user: userContext,
    };

    // Generate subscription checkout
    const checkoutInput: SubscriptionCheckoutInput = {
      subscriptionId,
      returnUrl,
      cancelUrl,
      metadata,
    };

    const checkoutSession = await generateSubscriptionCheckout(checkoutInput, enhancedContext);

    // Enhanced response for external integration
    const sessionData = {
      sessionId: checkoutSession.sessionId,
      sessionUrl: checkoutSession.sessionUrl,
      subscription: {
        id: subscription.id,
        name: subscription.name,
        description: subscription.description,
        price: subscription.price,
        interval: subscription.interval,
        creditsIncluded: subscription.creditsIncluded,
        features: JSON.parse(subscription.features),
      },
      returnUrl,
      cancelUrl,
      metadata,
      customerInfo,
      userId: userContext.id,
      createdAt: new Date(),
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
    };

    return res.status(201).json({
      success: true,
      message: 'External subscription checkout session created successfully',
      data: sessionData,
    });
  } catch (error) {
    console.error('❌ External subscription checkout creation failed:', error);
    
    if (error instanceof HttpError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message,
      });
    }

    return res.status(500).json({
      success: false,
      message: 'Internal server error during external subscription checkout creation',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

/**
 * Get checkout session status for external websites
 */
export const handleGetExternalCheckoutStatus = async (req: Request, res: Response, context: any) => {
  try {
    const { sessionId } = req.params;

    if (!sessionId) {
      return res.status(400).json({
        success: false,
        message: 'Session ID is required',
      });
    }

    // Get session status
    const sessionStatus = await getCheckoutSessionStatus(sessionId, context);

    // Enhanced status response for external integration
    const statusData = {
      sessionId,
      status: sessionStatus.status,
      planIdentifier: sessionStatus.planIdentifier,
      subscriptionId: sessionStatus.subscriptionId,
      paymentPlanId: sessionStatus.paymentPlanId,
      checkedAt: new Date(),
      // Add additional status information
      statusDetails: {
        isPending: sessionStatus.status === 'pending',
        isCompleted: sessionStatus.status === 'completed',
        isExpired: sessionStatus.status === 'expired',
        isCancelled: sessionStatus.status === 'cancelled',
      },
    };

    return res.status(200).json({
      success: true,
      message: 'Checkout session status retrieved successfully',
      data: statusData,
    });
  } catch (error) {
    console.error('❌ Failed to get external checkout session status:', error);
    
    if (error instanceof HttpError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message,
      });
    }

    return res.status(500).json({
      success: false,
      message: 'Internal server error while retrieving session status',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

/**
 * Get available subscription plans for external websites
 */
export const handleGetAvailableSubscriptions = async (req: Request, res: Response, context: any) => {
  try {
    // Get all active subscriptions
    const subscriptions = await context.entities.Subscription.findMany({
      where: { isActive: true },
      orderBy: [
        { price: 'asc' },
        { createdAt: 'asc' },
      ],
    });

    // Format subscriptions for external API
    const formattedSubscriptions = subscriptions.map((sub: any) => ({
      id: sub.id,
      name: sub.name,
      description: sub.description,
      price: sub.price,
      priceFormatted: `$${(sub.price / 100).toFixed(2)}`, // Convert cents to dollars
      duration: sub.duration,
      interval: sub.interval,
      creditsIncluded: sub.creditsIncluded,
      features: JSON.parse(sub.features),
      paymentProcessorPlanId: sub.paymentProcessorPlanId,
      isPopular: sub.name.toLowerCase().includes('pro'), // Simple popularity logic
    }));

    return res.status(200).json({
      success: true,
      message: 'Available subscriptions retrieved successfully',
      data: {
        subscriptions: formattedSubscriptions,
        total: formattedSubscriptions.length,
        currency: 'USD',
        lastUpdated: new Date(),
      },
    });
  } catch (error) {
    console.error('❌ Failed to get available subscriptions:', error);
    
    return res.status(500).json({
      success: false,
      message: 'Internal server error while retrieving subscriptions',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

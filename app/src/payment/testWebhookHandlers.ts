import { type Request, type Response } from 'express';
import { HttpError } from 'wasp/server';
import {
  handleEnhancedSubscriptionCreated,
  handleEnhancedSubscriptionUpdated,
  handleEnhancedSubscriptionCancelled,
  handleEnhancedSubscriptionExpired,
  type EnhancedWebhookContext,
} from './enhancedWebhookHandlers';

/**
 * Test API handler to verify the enhanced webhook handlers work
 * This simulates webhook events for testing the dual-system operation
 */
export const handleTestWebhookHandlers = async (req: Request, res: Response, context: any) => {
  try {
    // For testing, use a test user ID or create one
    let userId = req.body.userId;

    if (!userId) {
      // Create a test user for webhook testing
      const testUser = await context.entities.User.create({
        data: {
          email: `test-webhook-${Date.now()}@example.com`,
          credits: 0,
          queues: 1,
        },
      });
      userId = testUser.id;
    }

    console.log('🧪 Testing enhanced webhook handlers for user:', userId);

    // Create enhanced webhook context
    const enhancedContext: EnhancedWebhookContext = {
      userId,
      prismaUserDelegate: context.entities.User,
      prismaSubscriptionDelegate: context.entities.Subscription,
      prismaUserSubscriptionDelegate: context.entities.UserSubscription,
      entities: context.entities,
    };

    // Test data for subscription created event
    const testSubscriptionData = {
      data: {
        id: 'test_subscription_123',
        attributes: {
          customer_id: 12345,
          status: 'active',
          variant_id: 'test_hobby_plan_id', // This should match our test subscription
        },
      },
    };

    const results: any[] = [];

    try {
      // Test 1: Subscription Created
      console.log('🚀 Testing subscription created handler...');
      await handleEnhancedSubscriptionCreated(testSubscriptionData as any, enhancedContext);
      results.push({
        test: 'subscription_created',
        success: true,
        message: 'Successfully processed subscription created event',
      });
    } catch (error) {
      results.push({
        test: 'subscription_created',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }

    try {
      // Test 2: Subscription Updated
      console.log('🔄 Testing subscription updated handler...');
      const updatedSubscriptionData = {
        ...testSubscriptionData,
        data: {
          ...testSubscriptionData.data,
          attributes: {
            ...testSubscriptionData.data.attributes,
            status: 'past_due',
          },
        },
      };
      await handleEnhancedSubscriptionUpdated(updatedSubscriptionData as any, enhancedContext);
      results.push({
        test: 'subscription_updated',
        success: true,
        message: 'Successfully processed subscription updated event',
      });
    } catch (error) {
      results.push({
        test: 'subscription_updated',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }

    try {
      // Test 3: Subscription Cancelled
      console.log('❌ Testing subscription cancelled handler...');
      await handleEnhancedSubscriptionCancelled(testSubscriptionData as any, enhancedContext);
      results.push({
        test: 'subscription_cancelled',
        success: true,
        message: 'Successfully processed subscription cancelled event',
      });
    } catch (error) {
      results.push({
        test: 'subscription_cancelled',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }

    try {
      // Test 4: Subscription Expired
      console.log('⏰ Testing subscription expired handler...');
      await handleEnhancedSubscriptionExpired(testSubscriptionData as any, enhancedContext);
      results.push({
        test: 'subscription_expired',
        success: true,
        message: 'Successfully processed subscription expired event',
      });
    } catch (error) {
      results.push({
        test: 'subscription_expired',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }

    // Get current user subscription data to verify changes
    const userSubscriptions = await context.entities.UserSubscription.findMany({
      where: { userId },
      include: { subscription: true },
      orderBy: { createdAt: 'desc' },
    });

    const user = await context.entities.User.findUnique({
      where: { id: userId },
      select: {
        id: true,
        subscriptionStatus: true,
        subscriptionPlan: true,
        credits: true,
        queues: true,
        datePaid: true,
      },
    });

    const successCount = results.filter(r => r.success).length;
    const totalCount = results.length;

    return res.status(200).json({
      success: true,
      message: 'Enhanced webhook handlers test completed',
      data: {
        summary: {
          passed: successCount,
          total: totalCount,
          successRate: `${Math.round((successCount / totalCount) * 100)}%`,
        },
        results,
        userSubscriptions: userSubscriptions.map((sub: any) => ({
          id: sub.id,
          subscriptionId: sub.subscriptionId,
          status: sub.status,
          startDate: sub.startDate,
          endDate: sub.endDate,
          creditsAllocated: sub.creditsAllocated,
          paymentProcessorSubscriptionId: sub.paymentProcessorSubscriptionId,
          subscriptionName: sub.subscription?.name,
        })),
        userModel: user,
      },
    });
  } catch (error) {
    console.error('❌ Enhanced webhook handlers test failed:', error);
    
    if (error instanceof HttpError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message,
      });
    }

    return res.status(500).json({
      success: false,
      message: 'Internal server error during webhook handlers test',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

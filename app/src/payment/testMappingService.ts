import { type Request, type Response } from 'express';
import { HttpError } from 'wasp/server';
import { createSubscriptionMappingService, isPaymentPlanId, isSubscriptionId } from './subscriptionMapping';
import { PaymentPlanId } from './plans';

/**
 * Test API handler to verify the mapping service works
 * This will create test subscription records for development
 */
export const handleTestMappingService = async (req: Request, res: Response, context: any) => {
  try {
    console.log('🧪 Testing subscription mapping service...');

    // Create test subscription records directly for development
    const testSubscriptions = [
      {
        name: 'Free Plan',
        description: 'Basic plan with limited features for getting started',
        price: 0,
        duration: 30,
        interval: 'monthly',
        creditsIncluded: 30,
        features: JSON.stringify(['30 credits', '1 queue', 'Basic support']),
        isActive: true,
        paymentProcessorPlanId: 'test_free_plan_id',
      },
      {
        name: 'Hobby Plan',
        description: 'Perfect for individuals and small projects',
        price: 200,
        duration: 30,
        interval: 'monthly',
        creditsIncluded: 200,
        features: JSON.stringify(['200 credits', '3 queues', 'Email support']),
        isActive: true,
        paymentProcessorPlanId: 'test_hobby_plan_id',
      },
      {
        name: 'Pro Plan',
        description: 'Advanced features for professional use',
        price: 1000,
        duration: 30,
        interval: 'monthly',
        creditsIncluded: 1000,
        features: JSON.stringify(['1000 credits', '10 queues', 'Priority support', 'Advanced features']),
        isActive: true,
        paymentProcessorPlanId: 'test_pro_plan_id',
      },
      {
        name: '100 Credits Pack',
        description: 'One-time purchase of 100 credits',
        price: 1000,
        duration: 0,
        interval: 'one-time',
        creditsIncluded: 100,
        features: JSON.stringify(['100 credits', 'No expiration']),
        isActive: true,
        paymentProcessorPlanId: 'test_credits_plan_id',
      },
    ];

    // Create subscriptions if they don't exist
    let createdCount = 0;
    for (const subData of testSubscriptions) {
      const existing = await context.entities.Subscription.findFirst({
        where: { paymentProcessorPlanId: subData.paymentProcessorPlanId },
      });

      if (!existing) {
        await context.entities.Subscription.create({ data: subData });
        createdCount++;
      }
    }

    // Get all subscriptions
    const subscriptions = await context.entities.Subscription.findMany({
      orderBy: { createdAt: 'desc' },
    });

    // Test mapping functionality
    const mappingService = createSubscriptionMappingService(context.entities);
    const mappingTests = [];

    // Test mapping from test payment processor plan IDs to subscription IDs
    const testMappings = [
      { planId: 'test_free_plan_id', expectedName: 'Free Plan' },
      { planId: 'test_hobby_plan_id', expectedName: 'Hobby Plan' },
      { planId: 'test_pro_plan_id', expectedName: 'Pro Plan' },
      { planId: 'test_credits_plan_id', expectedName: '100 Credits Pack' },
    ];

    for (const mapping of testMappings) {
      try {
        const subscription = await context.entities.Subscription.findFirst({
          where: { paymentProcessorPlanId: mapping.planId },
        });

        if (subscription) {
          mappingTests.push({
            planId: mapping.planId,
            subscriptionId: subscription.id,
            name: subscription.name,
            success: subscription.name === mapping.expectedName,
          });
        }
      } catch (error) {
        mappingTests.push({
          planId: mapping.planId,
          error: error instanceof Error ? error.message : 'Unknown error',
          success: false,
        });
      }
    }

    return res.status(200).json({
      success: true,
      message: 'Mapping service test completed successfully',
      data: {
        subscriptionsCreated: createdCount,
        totalSubscriptions: subscriptions.length,
        mappingTests,
        subscriptions: subscriptions.map((sub: any) => ({
          id: sub.id,
          name: sub.name,
          paymentProcessorPlanId: sub.paymentProcessorPlanId,
          price: sub.price,
          interval: sub.interval,
          creditsIncluded: sub.creditsIncluded,
        })),
      },
    });
  } catch (error) {
    console.error('❌ Mapping service test failed:', error);
    return res.status(500).json({
      success: false,
      message: 'Mapping service test failed',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

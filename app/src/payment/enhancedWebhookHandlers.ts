import { PrismaClient } from '@prisma/client';
import { PaymentPlanId, paymentPlans, SubscriptionStatus } from './plans';
import { updateUserLemonSqueezyPaymentDetails } from './lemonSqueezy/paymentDetails';
import { createSubscriptionMappingService } from './subscriptionMapping';
import { type Order, type Subscription } from '@lemonsqueezy/lemonsqueezy.js';

/**
 * Enhanced webhook handlers that support both legacy User model updates
 * and new UserSubscription record creation for dual-system operation
 */

export interface EnhancedWebhookContext {
  userId: string;
  prismaUserDelegate: any;
  prismaSubscriptionDelegate: any;
  prismaUserSubscriptionDelegate: any;
  entities: any;
}

/**
 * Enhanced handler for subscription creation that supports both systems
 */
export async function handleEnhancedSubscriptionCreated(
  data: Subscription,
  context: EnhancedWebhookContext
): Promise<void> {
  console.log('🔄 Enhanced subscription created handler started');
  
  const { customer_id, status, variant_id } = data.data.attributes;
  const lemonSqueezyId = customer_id.toString();
  const { userId, entities } = context;

  try {
    // Get PaymentPlanId from variant_id
    const planId = getPlanIdByVariantId(variant_id.toString());
    const plan = paymentPlans[planId];

    if (status !== 'active') {
      console.warn(`Unexpected status '${status}' for newly created subscription`);
      return;
    }

    // For now, handle both systems separately since we don't have transaction support
    // 1. Create/Update UserSubscription record (new system)
    await createUserSubscriptionRecord(
      {
        userId,
        planId,
        plan,
        lemonSqueezySubscriptionId: data.data.id.toString(),
        paymentProcessorPlanId: variant_id.toString(),
        status: status as SubscriptionStatus,
      },
      entities
    );

    // 2. Update User model (legacy system for backward compatibility)
    await updateLegacyUserSubscription(
      {
        userId,
        planId,
        plan,
        lemonSqueezyId,
        status: status as SubscriptionStatus,
      },
      entities
    );

    console.log(`✅ Enhanced subscription created for user ${userId}`);
  } catch (error) {
    console.error(`❌ Enhanced subscription creation failed for user ${userId}:`, error);
    throw error;
  }
}

/**
 * Enhanced handler for subscription updates
 */
export async function handleEnhancedSubscriptionUpdated(
  data: Subscription,
  context: EnhancedWebhookContext
): Promise<void> {
  console.log('🔄 Enhanced subscription updated handler started');
  
  const { customer_id, status, variant_id } = data.data.attributes;
  const lemonSqueezyId = customer_id.toString();
  const { userId, entities } = context;

  try {
    const planId = getPlanIdByVariantId(variant_id.toString());
    const plan = paymentPlans[planId];

    // Only handle active and past_due statuses
    if (status === 'past_due' || status === 'active') {
      // 1. Update UserSubscription record
      await updateUserSubscriptionRecord(
        {
          userId,
          lemonSqueezySubscriptionId: data.data.id.toString(),
          status: status as SubscriptionStatus,
          planId,
        },
        entities
      );

      // 2. Update User model for backward compatibility
      await updateLegacyUserSubscription(
        {
          userId,
          planId,
          plan,
          lemonSqueezyId,
          status: status as SubscriptionStatus,
          updateCredits: status === 'active',
        },
        entities
      );

      console.log(`✅ Enhanced subscription updated for user ${userId}`);
    }
  } catch (error) {
    console.error(`❌ Enhanced subscription update failed for user ${userId}:`, error);
    throw error;
  }
}

/**
 * Enhanced handler for subscription cancellation
 */
export async function handleEnhancedSubscriptionCancelled(
  data: Subscription,
  context: EnhancedWebhookContext
): Promise<void> {
  console.log('🔄 Enhanced subscription cancelled handler started');
  
  const { customer_id } = data.data.attributes;
  const lemonSqueezyId = customer_id.toString();
  const { userId, entities } = context;

  try {
    // 1. Update UserSubscription record
    await updateUserSubscriptionRecord(
      {
        userId,
        lemonSqueezySubscriptionId: data.data.id.toString(),
        status: 'cancel_at_period_end' as SubscriptionStatus,
      },
      entities
    );

    // 2. Update User model for backward compatibility
    await entities.User.update({
      where: { id: userId },
      data: {
        subscriptionStatus: 'cancel_at_period_end' as SubscriptionStatus,
      },
    });

    console.log(`✅ Enhanced subscription cancelled for user ${userId}`);
  } catch (error) {
    console.error(`❌ Enhanced subscription cancellation failed for user ${userId}:`, error);
    throw error;
  }
}

/**
 * Enhanced handler for subscription expiration
 */
export async function handleEnhancedSubscriptionExpired(
  data: Subscription,
  context: EnhancedWebhookContext
): Promise<void> {
  console.log('🔄 Enhanced subscription expired handler started');
  
  const { customer_id } = data.data.attributes;
  const lemonSqueezyId = customer_id.toString();
  const { userId, entities } = context;

  try {
    // 1. Update UserSubscription record
    await updateUserSubscriptionRecord(
      {
        userId,
        lemonSqueezySubscriptionId: data.data.id.toString(),
        status: SubscriptionStatus.Deleted,
      },
      entities
    );

    // 2. Update User model for backward compatibility
    await entities.User.update({
      where: { id: userId },
      data: {
        subscriptionStatus: SubscriptionStatus.Deleted,
      },
    });

    console.log(`✅ Enhanced subscription expired for user ${userId}`);
  } catch (error) {
    console.error(`❌ Enhanced subscription expiration failed for user ${userId}:`, error);
    throw error;
  }
}

/**
 * Create UserSubscription record in the new system
 */
async function createUserSubscriptionRecord(
  params: {
    userId: string;
    planId: PaymentPlanId;
    plan: any;
    lemonSqueezySubscriptionId: string;
    paymentProcessorPlanId: string;
    status: SubscriptionStatus;
  },
  entities: any
): Promise<void> {
  const { userId, planId, plan, lemonSqueezySubscriptionId, paymentProcessorPlanId, status } = params;

  // Find corresponding Subscription record
  const subscription = await entities.Subscription.findFirst({
    where: { paymentProcessorPlanId },
  });

  if (!subscription) {
    console.warn(`No Subscription record found for payment processor plan ID: ${paymentProcessorPlanId}`);
    return;
  }

  // Calculate subscription dates
  const startDate = new Date();
  const endDate = plan.effect.type === 'one-time' ? null :
                  new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // 30 days from now

  // Create UserSubscription record
  await entities.UserSubscription.create({
    data: {
      userId,
      subscriptionId: subscription.id,
      status: mapLemonSqueezyStatusToUserSubscriptionStatus(status),
      startDate,
      endDate,
      creditsAllocated: plan.effect.amount,
      paymentProcessorSubscriptionId: lemonSqueezySubscriptionId,
    },
  });

  console.log(`✅ UserSubscription record created for user ${userId}, subscription ${subscription.id}`);
}

/**
 * Update existing UserSubscription record
 */
async function updateUserSubscriptionRecord(
  params: {
    userId: string;
    lemonSqueezySubscriptionId: string;
    status: SubscriptionStatus;
    planId?: PaymentPlanId;
  },
  entities: any
): Promise<void> {
  const { userId, lemonSqueezySubscriptionId, status, planId } = params;

  // Find existing UserSubscription record
  const userSubscription = await entities.UserSubscription.findFirst({
    where: {
      userId,
      paymentProcessorSubscriptionId: lemonSqueezySubscriptionId,
    },
  });

  if (!userSubscription) {
    console.warn(`No UserSubscription record found for user ${userId}, subscription ${lemonSqueezySubscriptionId}`);
    return;
  }

  // Update the record
  await entities.UserSubscription.update({
    where: { id: userSubscription.id },
    data: {
      status: mapLemonSqueezyStatusToUserSubscriptionStatus(status),
      ...(status === 'active' && { startDate: new Date() }),
    },
  });

  console.log(`✅ UserSubscription record updated for user ${userId}`);
}

/**
 * Update legacy User model for backward compatibility
 */
async function updateLegacyUserSubscription(
  params: {
    userId: string;
    planId: PaymentPlanId;
    plan: any;
    lemonSqueezyId: string;
    status: SubscriptionStatus;
    updateCredits?: boolean;
  },
  entities: any
): Promise<void> {
  const { userId, planId, plan, lemonSqueezyId, status, updateCredits = true } = params;

  const updateData: any = {
    paymentProcessorUserId: lemonSqueezyId,
    subscriptionPlan: planId,
    subscriptionStatus: status,
    datePaid: new Date(),
  };

  // Add credits if this is an active subscription and updateCredits is true
  if (status === 'active' && updateCredits && plan.effect.kind === 'subscription') {
    updateData.credits = { increment: plan.effect.amount };
    updateData.queues = plan.effect.queues || 1;
  }

  await entities.User.update({
    where: { id: userId },
    data: updateData,
  });

  console.log(`✅ Legacy User model updated for user ${userId}`);
}

/**
 * Map LemonSqueezy subscription status to UserSubscription status
 */
function mapLemonSqueezyStatusToUserSubscriptionStatus(status: SubscriptionStatus): string {
  const mapping: Record<SubscriptionStatus, string> = {
    [SubscriptionStatus.Active]: 'active',
    [SubscriptionStatus.PastDue]: 'active', // Keep as active but monitor
    [SubscriptionStatus.CancelAtPeriodEnd]: 'active', // Still active until period ends
    [SubscriptionStatus.Deleted]: 'expired',
  };

  return mapping[status] || 'pending';
}

/**
 * Get PaymentPlanId by variant ID (same as original function)
 */
function getPlanIdByVariantId(variantId: string): PaymentPlanId {
  const planId = Object.values(PaymentPlanId).find(
    (planId) => {
      try {
        return paymentPlans[planId].getPaymentProcessorPlanId() === variantId;
      } catch (error) {
        // Skip if environment variable is not set
        return false;
      }
    }
  );
  
  if (!planId) {
    throw new Error(`No plan with LemonSqueezy variant id ${variantId}`);
  }
  
  return planId;
}

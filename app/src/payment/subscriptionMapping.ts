import { PaymentPlanId, paymentPlans, PaymentPlanEffect } from './plans';
import { PrismaClient } from '@prisma/client';
import { HttpError } from 'wasp/server';

/**
 * Mapping service between legacy PaymentPlanId system and new Subscription model
 * Provides seamless integration during transition period
 */

export interface SubscriptionPlanMapping {
  subscriptionId: string;
  paymentPlanId: PaymentPlanId;
  paymentProcessorPlanId: string;
  name: string;
  description: string;
  price: number;
  duration: number;
  interval: string;
  creditsIncluded: number;
  features: string[];
  isActive: boolean;
}

export class SubscriptionMappingService {
  private prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  /**
   * Get Subscription record by legacy PaymentPlanId
   */
  async getSubscriptionByPaymentPlanId(paymentPlanId: PaymentPlanId): Promise<any> {
    const paymentPlan = paymentPlans[paymentPlanId];
    if (!paymentPlan) {
      throw new HttpError(404, `Payment plan not found: ${paymentPlanId}`);
    }

    const paymentProcessorPlanId = paymentPlan.getPaymentProcessorPlanId();
    
    const subscription = await this.prisma.subscription.findFirst({
      where: {
        paymentProcessorPlanId,
        isActive: true,
      },
    });

    if (!subscription) {
      throw new HttpError(404, `Subscription not found for payment plan: ${paymentPlanId}`);
    }

    return subscription;
  }

  /**
   * Get legacy PaymentPlanId by Subscription ID
   */
  async getPaymentPlanIdBySubscriptionId(subscriptionId: string): Promise<PaymentPlanId> {
    const subscription = await this.prisma.subscription.findUnique({
      where: { id: subscriptionId },
    });

    if (!subscription || !subscription.paymentProcessorPlanId) {
      throw new HttpError(404, `Subscription not found or missing payment processor plan ID: ${subscriptionId}`);
    }

    // Find matching PaymentPlanId by payment processor plan ID
    for (const [planId, plan] of Object.entries(paymentPlans)) {
      try {
        if (plan.getPaymentProcessorPlanId() === subscription.paymentProcessorPlanId) {
          return planId as PaymentPlanId;
        }
      } catch (error) {
        // Skip if environment variable is not set
        continue;
      }
    }

    throw new HttpError(404, `No matching PaymentPlanId found for subscription: ${subscriptionId}`);
  }

  /**
   * Get payment processor plan ID by Subscription ID
   */
  async getPaymentProcessorPlanIdBySubscriptionId(subscriptionId: string): Promise<string> {
    const subscription = await this.prisma.subscription.findUnique({
      where: { id: subscriptionId },
      select: { paymentProcessorPlanId: true },
    });

    if (!subscription?.paymentProcessorPlanId) {
      throw new HttpError(404, `Payment processor plan ID not found for subscription: ${subscriptionId}`);
    }

    return subscription.paymentProcessorPlanId;
  }

  /**
   * Create Subscription records for existing PaymentPlanId plans
   * This is used for migration and ensuring compatibility
   */
  async createSubscriptionsFromPaymentPlans(): Promise<void> {
    const subscriptionsToCreate: any[] = [];

    for (const [planId, plan] of Object.entries(paymentPlans)) {
      try {
        const paymentProcessorPlanId = plan.getPaymentProcessorPlanId();

        // Check if subscription already exists
        const existingSubscription = await this.prisma.subscription.findFirst({
          where: { paymentProcessorPlanId },
        });

        if (existingSubscription) {
          console.log(`Subscription already exists for plan ${planId}`);
          continue;
        }

        // Map PaymentPlanEffect to Subscription fields
        const subscriptionData = this.mapPaymentPlanToSubscription(planId as PaymentPlanId, plan.effect, paymentProcessorPlanId);
        subscriptionsToCreate.push(subscriptionData);
      } catch (error) {
        console.warn(`Skipping plan ${planId} due to missing environment variable:`, error);
      }
    }

    if (subscriptionsToCreate.length > 0) {
      await this.prisma.subscription.createMany({
        data: subscriptionsToCreate,
        skipDuplicates: true,
      });
      console.log(`Created ${subscriptionsToCreate.length} subscription records`);
    }
  }

  /**
   * Map PaymentPlanEffect to Subscription model fields
   */
  private mapPaymentPlanToSubscription(
    planId: PaymentPlanId,
    effect: PaymentPlanEffect,
    paymentProcessorPlanId: string
  ) {
    const planNames = {
      [PaymentPlanId.Free]: 'Free Plan',
      [PaymentPlanId.Hobby]: 'Hobby Plan',
      [PaymentPlanId.Pro]: 'Pro Plan',
      [PaymentPlanId.Credits10]: '100 Credits Pack',
    };

    const planDescriptions = {
      [PaymentPlanId.Free]: 'Basic plan with limited features for getting started',
      [PaymentPlanId.Hobby]: 'Perfect for individuals and small projects',
      [PaymentPlanId.Pro]: 'Advanced features for professional use',
      [PaymentPlanId.Credits10]: 'One-time purchase of 100 credits',
    };

    const planFeatures = {
      [PaymentPlanId.Free]: ['30 credits', '1 queue', 'Basic support'],
      [PaymentPlanId.Hobby]: ['200 credits', '3 queues', 'Email support'],
      [PaymentPlanId.Pro]: ['1000 credits', '10 queues', 'Priority support', 'Advanced features'],
      [PaymentPlanId.Credits10]: ['100 credits', 'No expiration'],
    };

    // Calculate price in cents (effect.amount is already in the right format)
    const price = effect.kind === 'credits' ? 1000 : // $10.00 for credits
                  planId === PaymentPlanId.Free ? 0 : // Free plan
                  planId === PaymentPlanId.Hobby ? 200 : // $2.00
                  planId === PaymentPlanId.Pro ? 1000 : 500; // $10.00

    return {
      name: planNames[planId],
      description: planDescriptions[planId],
      price,
      duration: effect.type === 'one-time' ? 0 : 30, // 30 days for subscriptions, 0 for one-time
      interval: effect.type === 'one-time' ? 'one-time' : 'monthly',
      creditsIncluded: effect.amount,
      features: JSON.stringify(planFeatures[planId]),
      isActive: true,
      paymentProcessorPlanId,
    };
  }

  /**
   * Get unified plan information that works with both systems
   */
  async getUnifiedPlanInfo(identifier: string): Promise<SubscriptionPlanMapping> {
    let subscription: any;
    let paymentPlanId: PaymentPlanId;

    // Try to determine if identifier is a PaymentPlanId or Subscription ID
    if (Object.values(PaymentPlanId).includes(identifier as PaymentPlanId)) {
      // It's a PaymentPlanId
      paymentPlanId = identifier as PaymentPlanId;
      subscription = await this.getSubscriptionByPaymentPlanId(paymentPlanId);
    } else {
      // It's a Subscription ID
      subscription = await this.prisma.subscription.findUnique({
        where: { id: identifier },
      });
      
      if (!subscription) {
        throw new HttpError(404, `Subscription not found: ${identifier}`);
      }
      
      paymentPlanId = await this.getPaymentPlanIdBySubscriptionId(identifier);
    }

    return {
      subscriptionId: subscription.id,
      paymentPlanId,
      paymentProcessorPlanId: subscription.paymentProcessorPlanId,
      name: subscription.name,
      description: subscription.description,
      price: subscription.price,
      duration: subscription.duration,
      interval: subscription.interval,
      creditsIncluded: subscription.creditsIncluded,
      features: JSON.parse(subscription.features),
      isActive: subscription.isActive,
    };
  }
}

/**
 * Helper function to create mapping service instance
 */
export function createSubscriptionMappingService(prisma: PrismaClient): SubscriptionMappingService {
  return new SubscriptionMappingService(prisma);
}

/**
 * Utility function to check if identifier is a PaymentPlanId
 */
export function isPaymentPlanId(identifier: string): boolean {
  return Object.values(PaymentPlanId).includes(identifier as PaymentPlanId);
}

/**
 * Utility function to check if identifier is a UUID (Subscription ID)
 */
export function isSubscriptionId(identifier: string): boolean {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(identifier);
}

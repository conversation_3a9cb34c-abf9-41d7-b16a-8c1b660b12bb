import { type Request, type Response } from 'express';
import { HttpError } from 'wasp/server';
import { z } from 'zod';
import {
  generateEnhancedCheckoutSession,
  generateSubscriptionCheckout,
  getCheckoutSessionStatus,
  validateCheckoutInput,
  subscriptionCheckoutSchema,
  type SubscriptionCheckoutInput,
} from './enhancedCheckout';

/**
 * API handler for enhanced checkout session generation
 * Supports both legacy PaymentPlanId and new Subscription IDs
 */
export const handleCreateEnhancedCheckout = async (req: Request, res: Response, context: any) => {
  try {
    // Check authentication
    if (!context.user) {
      throw new HttpError(401, 'User not authenticated');
    }

    const { planIdentifier } = req.body;

    if (!planIdentifier) {
      return res.status(400).json({
        success: false,
        message: 'Plan identifier is required',
        error: 'Missing planIdentifier in request body',
      });
    }

    // Validate input
    const validation = validateCheckoutInput(planIdentifier);
    if (!validation.isValid) {
      return res.status(400).json({
        success: false,
        message: 'Invalid plan identifier',
        errors: validation.errors,
      });
    }

    // Generate checkout session
    const checkoutSession = await generateEnhancedCheckoutSession(planIdentifier, context);

    return res.status(200).json({
      success: true,
      message: 'Checkout session created successfully',
      data: {
        sessionUrl: checkoutSession.sessionUrl,
        sessionId: checkoutSession.sessionId,
        planType: checkoutSession.planType,
        planIdentifier: checkoutSession.planIdentifier,
        subscriptionId: checkoutSession.subscriptionId,
        paymentPlanId: checkoutSession.paymentPlanId,
      },
    });
  } catch (error) {
    console.error('❌ Enhanced checkout creation failed:', error);
    
    if (error instanceof HttpError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message,
      });
    }

    return res.status(500).json({
      success: false,
      message: 'Internal server error during checkout creation',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

/**
 * API handler for subscription-specific checkout with additional options
 */
export const handleCreateSubscriptionCheckout = async (req: Request, res: Response, context: any) => {
  try {
    // Check authentication
    if (!context.user) {
      throw new HttpError(401, 'User not authenticated');
    }

    // Validate request body
    const parsedBody = subscriptionCheckoutSchema.safeParse(req.body);
    if (!parsedBody.success) {
      return res.status(400).json({
        success: false,
        message: 'Invalid request body',
        errors: parsedBody.error.format(),
      });
    }

    const input: SubscriptionCheckoutInput = parsedBody.data;

    // Generate subscription checkout session
    const checkoutSession = await generateSubscriptionCheckout(input, context);

    return res.status(200).json({
      success: true,
      message: 'Subscription checkout session created successfully',
      data: {
        sessionUrl: checkoutSession.sessionUrl,
        sessionId: checkoutSession.sessionId,
        planType: checkoutSession.planType,
        subscriptionId: checkoutSession.subscriptionId,
        returnUrl: input.returnUrl,
        cancelUrl: input.cancelUrl,
        metadata: input.metadata,
      },
    });
  } catch (error) {
    console.error('❌ Subscription checkout creation failed:', error);
    
    if (error instanceof HttpError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message,
      });
    }

    return res.status(500).json({
      success: false,
      message: 'Internal server error during subscription checkout creation',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

/**
 * API handler for checking checkout session status
 */
export const handleGetCheckoutSessionStatus = async (req: Request, res: Response, context: any) => {
  try {
    // Check authentication
    if (!context.user) {
      throw new HttpError(401, 'User not authenticated');
    }

    const { sessionId } = req.params;

    if (!sessionId) {
      return res.status(400).json({
        success: false,
        message: 'Session ID is required',
      });
    }

    // Get checkout session status
    const sessionStatus = await getCheckoutSessionStatus(sessionId, context);

    return res.status(200).json({
      success: true,
      message: 'Checkout session status retrieved successfully',
      data: sessionStatus,
    });
  } catch (error) {
    console.error('❌ Failed to get checkout session status:', error);
    
    if (error instanceof HttpError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message,
      });
    }

    return res.status(500).json({
      success: false,
      message: 'Internal server error while retrieving session status',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

/**
 * API handler for validating checkout input (utility endpoint)
 */
export const handleValidateCheckoutInput = async (req: Request, res: Response, context: any) => {
  try {
    const { input } = req.body;

    const validation = validateCheckoutInput(input);

    return res.status(200).json({
      success: true,
      message: 'Input validation completed',
      data: validation,
    });
  } catch (error) {
    console.error('❌ Input validation failed:', error);
    
    return res.status(500).json({
      success: false,
      message: 'Internal server error during input validation',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

/**
 * API handler for testing checkout compatibility
 */
export const handleTestCheckoutCompatibility = async (req: Request, res: Response, context: any) => {
  try {
    const testCases = [
      { input: 'hobby', expectedType: 'legacy' },
      { input: 'pro', expectedType: 'legacy' },
      { input: 'free', expectedType: 'legacy' },
      { input: 'credits10', expectedType: 'legacy' },
      { input: '39b2bc9e-9fde-4120-ac3d-ca8e3f11707b', expectedType: 'subscription' }, // Test subscription ID
      { input: 'invalid-input', expectedType: 'unknown' },
    ];

    const results = testCases.map(testCase => {
      const validation = validateCheckoutInput(testCase.input);
      return {
        input: testCase.input,
        expectedType: testCase.expectedType,
        actualType: validation.planType,
        isValid: validation.isValid,
        success: validation.planType === testCase.expectedType,
        errors: validation.errors,
      };
    });

    const successCount = results.filter(r => r.success).length;
    const totalCount = results.length;

    return res.status(200).json({
      success: true,
      message: 'Checkout compatibility test completed',
      data: {
        summary: {
          passed: successCount,
          total: totalCount,
          successRate: `${Math.round((successCount / totalCount) * 100)}%`,
        },
        results,
      },
    });
  } catch (error) {
    console.error('❌ Checkout compatibility test failed:', error);
    
    return res.status(500).json({
      success: false,
      message: 'Internal server error during compatibility test',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

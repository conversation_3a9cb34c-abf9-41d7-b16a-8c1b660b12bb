import { type Request, type Response } from 'express';
import { HttpError } from 'wasp/server';
import { z } from 'zod';

/**
 * User-specific subscription management APIs
 * Designed for external website integration with comprehensive user data management
 */

// Validation schemas
const updateCreditsSchema = z.object({
  credits: z.number().min(0),
  operation: z.enum(['set', 'add', 'subtract']).default('set'),
  reason: z.string().max(500).optional(),
});

const updateUserSubscriptionSchema = z.object({
  status: z.enum(['active', 'cancelled', 'expired', 'pending']).optional(),
  creditsAllocated: z.number().min(0).optional(),
  endDate: z.string().datetime().optional(),
});

/**
 * Get user subscription overview
 */
export const handleGetUserSubscriptionOverview = async (req: Request, res: Response, context: any) => {
  try {
    // Support both authenticated users and external API with user ID
    let userId = context.user?.id;
    
    // For external API, allow specifying user ID in query params
    if (!userId && req.query.userId) {
      userId = req.query.userId as string;
    }

    if (!userId) {
      throw new HttpError(400, 'User ID required. Either authenticate or provide userId parameter.');
    }

    // Get user details
    const user = await context.entities.User.findUnique({
      where: { id: userId },
      select: {
        id: true,
        email: true,
        credits: true,
        queues: true,
        subscriptionStatus: true,
        subscriptionPlan: true,
        createdAt: true,
      },
    });

    if (!user) {
      throw new HttpError(404, 'User not found');
    }

    // Get current active subscription
    const currentSubscription = await context.entities.UserSubscription.findFirst({
      where: {
        userId,
        status: 'active',
      },
      include: {
        subscription: true,
      },
      orderBy: { createdAt: 'desc' },
    });

    // Get subscription statistics
    const subscriptionStats = await context.entities.UserSubscription.aggregate({
      where: { userId },
      _count: {
        id: true,
      },
    });

    const activeSubscriptionsCount = await context.entities.UserSubscription.count({
      where: {
        userId,
        status: 'active',
      },
    });

    // Calculate subscription metrics
    let subscriptionMetrics: any = null;
    if (currentSubscription) {
      const now = new Date();
      const daysRemaining = currentSubscription.endDate
        ? Math.max(0, Math.ceil((new Date(currentSubscription.endDate).getTime() - now.getTime()) / (1000 * 60 * 60 * 24)))
        : null;

      subscriptionMetrics = {
        id: currentSubscription.id,
        subscriptionId: currentSubscription.subscriptionId,
        name: currentSubscription.subscription.name,
        status: currentSubscription.status,
        startDate: currentSubscription.startDate,
        endDate: currentSubscription.endDate,
        daysRemaining,
        creditsAllocated: currentSubscription.creditsAllocated,
        price: currentSubscription.subscription.price,
        priceFormatted: `$${(currentSubscription.subscription.price / 100).toFixed(2)}`,
        interval: currentSubscription.subscription.interval,
        features: JSON.parse(currentSubscription.subscription.features),
      };
    }

    const overview = {
      user: {
        id: user.id,
        email: user.email,
        credits: user.credits,
        queues: user.queues,
        legacySubscriptionStatus: user.subscriptionStatus,
        legacySubscriptionPlan: user.subscriptionPlan,
        memberSince: user.createdAt,
      },
      currentSubscription: subscriptionMetrics,
      statistics: {
        totalSubscriptions: subscriptionStats._count.id,
        activeSubscriptions: activeSubscriptionsCount,
        hasActiveSubscription: !!currentSubscription,
      },
      lastUpdated: new Date(),
    };

    return res.status(200).json({
      success: true,
      message: 'User subscription overview retrieved successfully',
      data: overview,
    });
  } catch (error) {
    console.error('❌ Failed to get user subscription overview:', error);
    
    if (error instanceof HttpError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message,
      });
    }

    return res.status(500).json({
      success: false,
      message: 'Internal server error while retrieving user subscription overview',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

/**
 * Update user credits
 */
export const handleUpdateUserCredits = async (req: Request, res: Response, context: any) => {
  try {
    // Support both authenticated users and external API with user ID
    let userId = context.user?.id;
    
    // For external API, allow specifying user ID in body
    if (!userId && req.body.userId) {
      userId = req.body.userId;
    }

    if (!userId) {
      throw new HttpError(400, 'User ID required. Either authenticate or provide userId in request body.');
    }

    // Validate request body
    const parsedBody = updateCreditsSchema.safeParse(req.body);
    if (!parsedBody.success) {
      return res.status(400).json({
        success: false,
        message: 'Invalid request body',
        errors: parsedBody.error.format(),
      });
    }

    const { credits, operation, reason } = parsedBody.data;

    // Get current user
    const user = await context.entities.User.findUnique({
      where: { id: userId },
    });

    if (!user) {
      throw new HttpError(404, 'User not found');
    }

    // Calculate new credits based on operation
    let newCredits: number;
    switch (operation) {
      case 'set':
        newCredits = credits;
        break;
      case 'add':
        newCredits = user.credits + credits;
        break;
      case 'subtract':
        newCredits = Math.max(0, user.credits - credits);
        break;
      default:
        throw new HttpError(400, 'Invalid operation');
    }

    // Update user credits
    const updatedUser = await context.entities.User.update({
      where: { id: userId },
      data: { credits: newCredits },
    });

    // Log the credit change
    console.log(`Credits updated for user ${userId}: ${user.credits} -> ${newCredits} (${operation} ${credits}). Reason: ${reason || 'Not specified'}`);

    return res.status(200).json({
      success: true,
      message: 'User credits updated successfully',
      data: {
        userId,
        previousCredits: user.credits,
        newCredits: updatedUser.credits,
        operation,
        amount: credits,
        reason,
        updatedAt: new Date(),
      },
    });
  } catch (error) {
    console.error('❌ Failed to update user credits:', error);
    
    if (error instanceof HttpError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message,
      });
    }

    return res.status(500).json({
      success: false,
      message: 'Internal server error while updating user credits',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

/**
 * Get user credit history (simulated)
 */
export const handleGetUserCreditHistory = async (req: Request, res: Response, context: any) => {
  try {
    // Support both authenticated users and external API with user ID
    let userId = context.user?.id;
    
    // For external API, allow specifying user ID in query params
    if (!userId && req.query.userId) {
      userId = req.query.userId as string;
    }

    if (!userId) {
      throw new HttpError(400, 'User ID required. Either authenticate or provide userId parameter.');
    }

    const { limit = 10, offset = 0 } = req.query;

    // Get user
    const user = await context.entities.User.findUnique({
      where: { id: userId },
    });

    if (!user) {
      throw new HttpError(404, 'User not found');
    }

    // Since we don't have a credit history table, we'll simulate it based on subscriptions
    const userSubscriptions = await context.entities.UserSubscription.findMany({
      where: { userId },
      include: {
        subscription: true,
      },
      orderBy: { createdAt: 'desc' },
      take: parseInt(limit as string),
      skip: parseInt(offset as string),
    });

    // Simulate credit history from subscriptions
    const creditHistory = userSubscriptions.map((sub, index) => ({
      id: `credit_${sub.id}`,
      type: 'subscription_allocation',
      amount: sub.creditsAllocated,
      operation: 'add',
      balance: user.credits, // Current balance (simplified)
      description: `Credits allocated from ${sub.subscription.name} subscription`,
      subscriptionId: sub.id,
      subscriptionName: sub.subscription.name,
      createdAt: sub.createdAt,
    }));

    // Add current balance as the most recent entry
    if (creditHistory.length === 0 || parseInt(offset as string) === 0) {
      creditHistory.unshift({
        id: `current_balance_${userId}`,
        type: 'current_balance',
        amount: user.credits,
        operation: 'balance',
        balance: user.credits,
        description: 'Current credit balance',
        subscriptionId: null,
        subscriptionName: null,
        createdAt: new Date(),
      });
    }

    return res.status(200).json({
      success: true,
      message: 'User credit history retrieved successfully',
      data: {
        userId,
        currentCredits: user.credits,
        history: creditHistory,
        pagination: {
          limit: parseInt(limit as string),
          offset: parseInt(offset as string),
          total: creditHistory.length,
        },
      },
    });
  } catch (error) {
    console.error('❌ Failed to get user credit history:', error);
    
    if (error instanceof HttpError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message,
      });
    }

    return res.status(500).json({
      success: false,
      message: 'Internal server error while retrieving user credit history',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

/**
 * Update user subscription details
 */
export const handleUpdateUserSubscription = async (req: Request, res: Response, context: any) => {
  try {
    // Support both authenticated users and external API with user ID
    let userId = context.user?.id;
    
    // For external API, allow specifying user ID in body
    if (!userId && req.body.userId) {
      userId = req.body.userId;
    }

    if (!userId) {
      throw new HttpError(400, 'User ID required. Either authenticate or provide userId in request body.');
    }

    // Validate request body
    const parsedBody = updateUserSubscriptionSchema.safeParse(req.body);
    if (!parsedBody.success) {
      return res.status(400).json({
        success: false,
        message: 'Invalid request body',
        errors: parsedBody.error.format(),
      });
    }

    const { status, creditsAllocated, endDate } = parsedBody.data;

    // Get current active subscription
    const currentSubscription = await context.entities.UserSubscription.findFirst({
      where: {
        userId,
        status: 'active',
      },
      include: {
        subscription: true,
      },
    });

    if (!currentSubscription) {
      return res.status(404).json({
        success: false,
        message: 'No active subscription found for user',
      });
    }

    // Prepare update data
    const updateData: any = {};
    if (status) updateData.status = status;
    if (creditsAllocated !== undefined) updateData.creditsAllocated = creditsAllocated;
    if (endDate) updateData.endDate = new Date(endDate);

    // Update subscription
    const updatedSubscription = await context.entities.UserSubscription.update({
      where: { id: currentSubscription.id },
      data: updateData,
      include: {
        subscription: true,
      },
    });

    return res.status(200).json({
      success: true,
      message: 'User subscription updated successfully',
      data: {
        subscriptionId: updatedSubscription.id,
        userId,
        previousData: {
          status: currentSubscription.status,
          creditsAllocated: currentSubscription.creditsAllocated,
          endDate: currentSubscription.endDate,
        },
        newData: {
          status: updatedSubscription.status,
          creditsAllocated: updatedSubscription.creditsAllocated,
          endDate: updatedSubscription.endDate,
        },
        subscription: {
          id: updatedSubscription.subscription.id,
          name: updatedSubscription.subscription.name,
          price: updatedSubscription.subscription.price,
        },
        updatedAt: updatedSubscription.updatedAt,
      },
    });
  } catch (error) {
    console.error('❌ Failed to update user subscription:', error);
    
    if (error instanceof HttpError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message,
      });
    }

    return res.status(500).json({
      success: false,
      message: 'Internal server error while updating user subscription',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

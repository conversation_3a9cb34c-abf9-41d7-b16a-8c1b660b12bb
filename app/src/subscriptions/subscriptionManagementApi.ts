import { type Request, type Response } from 'express';
import { HttpError } from 'wasp/server';
import { z } from 'zod';
import { createSubscriptionMappingService } from '../payment/subscriptionMapping';

/**
 * Comprehensive subscription management APIs
 * Provides full CRUD operations for subscription lifecycle management
 */

// Validation schemas
const subscriptionUpdateSchema = z.object({
  name: z.string().min(1).max(100).optional(),
  description: z.string().max(500).optional(),
  price: z.number().min(0).optional(),
  creditsIncluded: z.number().min(0).optional(),
  features: z.array(z.string()).optional(),
  isActive: z.boolean().optional(),
});

const userSubscriptionUpdateSchema = z.object({
  status: z.enum(['active', 'cancelled', 'expired', 'pending']).optional(),
  creditsAllocated: z.number().min(0).optional(),
  endDate: z.string().datetime().optional(),
});

const subscriptionCancelSchema = z.object({
  reason: z.string().max(500).optional(),
  cancelAtPeriodEnd: z.boolean().default(true),
});

const subscriptionUpgradeSchema = z.object({
  newSubscriptionId: z.string().uuid(),
  prorationMode: z.enum(['immediate', 'next_billing_cycle']).default('immediate'),
});

/**
 * Get user's subscription history
 */
export const handleGetUserSubscriptionHistory = async (req: Request, res: Response, context: any) => {
  try {
    // Check authentication
    if (!context.user) {
      throw new HttpError(401, 'User not authenticated');
    }

    const userId = context.user.id;
    const { status, limit = 10, offset = 0 } = req.query;

    // Build query filters
    const whereClause: any = { userId };
    if (status) {
      whereClause.status = status;
    }

    // Get user subscriptions with pagination
    const userSubscriptions = await context.entities.UserSubscription.findMany({
      where: whereClause,
      include: {
        subscription: true,
      },
      orderBy: { createdAt: 'desc' },
      take: parseInt(limit as string),
      skip: parseInt(offset as string),
    });

    // Get total count for pagination
    const totalCount = await context.entities.UserSubscription.count({
      where: whereClause,
    });

    // Format response
    const formattedSubscriptions = userSubscriptions.map((userSub: any) => ({
      id: userSub.id,
      subscriptionId: userSub.subscriptionId,
      status: userSub.status,
      startDate: userSub.startDate,
      endDate: userSub.endDate,
      creditsAllocated: userSub.creditsAllocated,
      paymentProcessorSubscriptionId: userSub.paymentProcessorSubscriptionId,
      subscription: {
        id: userSub.subscription.id,
        name: userSub.subscription.name,
        description: userSub.subscription.description,
        price: userSub.subscription.price,
        priceFormatted: `$${(userSub.subscription.price / 100).toFixed(2)}`,
        interval: userSub.subscription.interval,
        creditsIncluded: userSub.subscription.creditsIncluded,
        features: JSON.parse(userSub.subscription.features),
      },
      createdAt: userSub.createdAt,
      updatedAt: userSub.updatedAt,
    }));

    return res.status(200).json({
      success: true,
      message: 'User subscription history retrieved successfully',
      data: {
        subscriptions: formattedSubscriptions,
        pagination: {
          total: totalCount,
          limit: parseInt(limit as string),
          offset: parseInt(offset as string),
          hasMore: parseInt(offset as string) + parseInt(limit as string) < totalCount,
        },
      },
    });
  } catch (error) {
    console.error('❌ Failed to get user subscription history:', error);
    
    if (error instanceof HttpError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message,
      });
    }

    return res.status(500).json({
      success: false,
      message: 'Internal server error while retrieving subscription history',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

/**
 * Get current active subscription for user
 */
export const handleGetCurrentSubscription = async (req: Request, res: Response, context: any) => {
  try {
    // Check authentication
    if (!context.user) {
      throw new HttpError(401, 'User not authenticated');
    }

    const userId = context.user.id;

    // Get current active subscription
    const currentSubscription = await context.entities.UserSubscription.findFirst({
      where: {
        userId,
        status: 'active',
      },
      include: {
        subscription: true,
      },
      orderBy: { createdAt: 'desc' },
    });

    if (!currentSubscription) {
      return res.status(404).json({
        success: false,
        message: 'No active subscription found for user',
      });
    }

    // Calculate subscription metrics
    const now = new Date();
    const daysRemaining = currentSubscription.endDate 
      ? Math.max(0, Math.ceil((new Date(currentSubscription.endDate).getTime() - now.getTime()) / (1000 * 60 * 60 * 24)))
      : null;

    const subscriptionData = {
      id: currentSubscription.id,
      subscriptionId: currentSubscription.subscriptionId,
      status: currentSubscription.status,
      startDate: currentSubscription.startDate,
      endDate: currentSubscription.endDate,
      daysRemaining,
      creditsAllocated: currentSubscription.creditsAllocated,
      paymentProcessorSubscriptionId: currentSubscription.paymentProcessorSubscriptionId,
      subscription: {
        id: currentSubscription.subscription.id,
        name: currentSubscription.subscription.name,
        description: currentSubscription.subscription.description,
        price: currentSubscription.subscription.price,
        priceFormatted: `$${(currentSubscription.subscription.price / 100).toFixed(2)}`,
        interval: currentSubscription.subscription.interval,
        creditsIncluded: currentSubscription.subscription.creditsIncluded,
        features: JSON.parse(currentSubscription.subscription.features),
      },
      createdAt: currentSubscription.createdAt,
      updatedAt: currentSubscription.updatedAt,
    };

    return res.status(200).json({
      success: true,
      message: 'Current subscription retrieved successfully',
      data: subscriptionData,
    });
  } catch (error) {
    console.error('❌ Failed to get current subscription:', error);
    
    if (error instanceof HttpError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message,
      });
    }

    return res.status(500).json({
      success: false,
      message: 'Internal server error while retrieving current subscription',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

/**
 * Cancel user subscription
 */
export const handleCancelSubscription = async (req: Request, res: Response, context: any) => {
  try {
    // Check authentication
    if (!context.user) {
      throw new HttpError(401, 'User not authenticated');
    }

    // Validate request body
    const parsedBody = subscriptionCancelSchema.safeParse(req.body);
    if (!parsedBody.success) {
      return res.status(400).json({
        success: false,
        message: 'Invalid request body',
        errors: parsedBody.error.format(),
      });
    }

    const { reason, cancelAtPeriodEnd } = parsedBody.data;
    const userId = context.user.id;

    // Get current active subscription
    const currentSubscription = await context.entities.UserSubscription.findFirst({
      where: {
        userId,
        status: 'active',
      },
      include: {
        subscription: true,
      },
    });

    if (!currentSubscription) {
      return res.status(404).json({
        success: false,
        message: 'No active subscription found to cancel',
      });
    }

    // Update subscription status
    const newStatus = cancelAtPeriodEnd ? 'active' : 'cancelled'; // Keep active until period end
    const updatedSubscription = await context.entities.UserSubscription.update({
      where: { id: currentSubscription.id },
      data: {
        status: newStatus,
        ...(reason && { 
          // Store cancellation reason in a metadata field if available
          // For now, we'll just log it
        }),
      },
      include: {
        subscription: true,
      },
    });

    // Log cancellation reason
    if (reason) {
      console.log(`Subscription cancelled for user ${userId}. Reason: ${reason}`);
    }

    return res.status(200).json({
      success: true,
      message: cancelAtPeriodEnd 
        ? 'Subscription will be cancelled at the end of the current billing period'
        : 'Subscription cancelled immediately',
      data: {
        subscriptionId: updatedSubscription.id,
        status: updatedSubscription.status,
        cancelAtPeriodEnd,
        endDate: updatedSubscription.endDate,
        reason,
        cancelledAt: new Date(),
      },
    });
  } catch (error) {
    console.error('❌ Failed to cancel subscription:', error);
    
    if (error instanceof HttpError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message,
      });
    }

    return res.status(500).json({
      success: false,
      message: 'Internal server error while cancelling subscription',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

/**
 * Reactivate cancelled subscription
 */
export const handleReactivateSubscription = async (req: Request, res: Response, context: any) => {
  try {
    // Check authentication
    if (!context.user) {
      throw new HttpError(401, 'User not authenticated');
    }

    const userId = context.user.id;

    // Get most recent cancelled subscription
    const cancelledSubscription = await context.entities.UserSubscription.findFirst({
      where: {
        userId,
        status: { in: ['cancelled', 'expired'] },
      },
      include: {
        subscription: true,
      },
      orderBy: { updatedAt: 'desc' },
    });

    if (!cancelledSubscription) {
      return res.status(404).json({
        success: false,
        message: 'No cancelled subscription found to reactivate',
      });
    }

    // Check if subscription can be reactivated (within reasonable time frame)
    const daysSinceCancellation = Math.ceil(
      (new Date().getTime() - new Date(cancelledSubscription.updatedAt).getTime()) / (1000 * 60 * 60 * 24)
    );

    if (daysSinceCancellation > 30) {
      return res.status(400).json({
        success: false,
        message: 'Subscription cannot be reactivated after 30 days. Please create a new subscription.',
      });
    }

    // Reactivate subscription
    const reactivatedSubscription = await context.entities.UserSubscription.update({
      where: { id: cancelledSubscription.id },
      data: {
        status: 'active',
        startDate: new Date(),
        endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      },
      include: {
        subscription: true,
      },
    });

    return res.status(200).json({
      success: true,
      message: 'Subscription reactivated successfully',
      data: {
        subscriptionId: reactivatedSubscription.id,
        status: reactivatedSubscription.status,
        startDate: reactivatedSubscription.startDate,
        endDate: reactivatedSubscription.endDate,
        reactivatedAt: new Date(),
      },
    });
  } catch (error) {
    console.error('❌ Failed to reactivate subscription:', error);
    
    if (error instanceof HttpError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message,
      });
    }

    return res.status(500).json({
      success: false,
      message: 'Internal server error while reactivating subscription',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

/**
 * Upgrade/downgrade subscription
 */
export const handleUpgradeSubscription = async (req: Request, res: Response, context: any) => {
  try {
    // Check authentication
    if (!context.user) {
      throw new HttpError(401, 'User not authenticated');
    }

    // Validate request body
    const parsedBody = subscriptionUpgradeSchema.safeParse(req.body);
    if (!parsedBody.success) {
      return res.status(400).json({
        success: false,
        message: 'Invalid request body',
        errors: parsedBody.error.format(),
      });
    }

    const { newSubscriptionId, prorationMode } = parsedBody.data;
    const userId = context.user.id;

    // Get current active subscription
    const currentSubscription = await context.entities.UserSubscription.findFirst({
      where: {
        userId,
        status: 'active',
      },
      include: {
        subscription: true,
      },
    });

    if (!currentSubscription) {
      return res.status(404).json({
        success: false,
        message: 'No active subscription found to upgrade/downgrade',
      });
    }

    // Get new subscription details
    const newSubscription = await context.entities.Subscription.findUnique({
      where: { id: newSubscriptionId },
    });

    if (!newSubscription || !newSubscription.isActive) {
      return res.status(404).json({
        success: false,
        message: 'Target subscription not found or inactive',
      });
    }

    // Prevent upgrading to the same subscription
    if (currentSubscription.subscriptionId === newSubscriptionId) {
      return res.status(400).json({
        success: false,
        message: 'Cannot upgrade to the same subscription plan',
      });
    }

    // Determine if this is an upgrade or downgrade
    const isUpgrade = newSubscription.price > currentSubscription.subscription.price;
    const changeType = isUpgrade ? 'upgrade' : 'downgrade';

    // Calculate proration if immediate
    let prorationAmount = 0;
    if (prorationMode === 'immediate') {
      const daysRemaining = currentSubscription.endDate
        ? Math.max(0, Math.ceil((new Date(currentSubscription.endDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)))
        : 0;

      if (daysRemaining > 0) {
        const dailyOldRate = currentSubscription.subscription.price / 30;
        const dailyNewRate = newSubscription.price / 30;
        prorationAmount = (dailyNewRate - dailyOldRate) * daysRemaining;
      }
    }

    // End current subscription
    await context.entities.UserSubscription.update({
      where: { id: currentSubscription.id },
      data: {
        status: 'cancelled',
        endDate: prorationMode === 'immediate' ? new Date() : currentSubscription.endDate,
      },
    });

    // Create new subscription
    const startDate = prorationMode === 'immediate' ? new Date() : currentSubscription.endDate || new Date();
    const endDate = new Date(startDate.getTime() + newSubscription.duration * 24 * 60 * 60 * 1000);

    const newUserSubscription = await context.entities.UserSubscription.create({
      data: {
        userId,
        subscriptionId: newSubscriptionId,
        status: 'active',
        startDate,
        endDate,
        creditsAllocated: newSubscription.creditsIncluded,
      },
      include: {
        subscription: true,
      },
    });

    return res.status(200).json({
      success: true,
      message: `Subscription ${changeType} completed successfully`,
      data: {
        changeType,
        previousSubscription: {
          id: currentSubscription.id,
          name: currentSubscription.subscription.name,
          price: currentSubscription.subscription.price,
        },
        newSubscription: {
          id: newUserSubscription.id,
          subscriptionId: newUserSubscription.subscriptionId,
          name: newUserSubscription.subscription.name,
          price: newUserSubscription.subscription.price,
          startDate: newUserSubscription.startDate,
          endDate: newUserSubscription.endDate,
          creditsAllocated: newUserSubscription.creditsAllocated,
        },
        prorationMode,
        prorationAmount: Math.round(prorationAmount),
        effectiveDate: startDate,
      },
    });
  } catch (error) {
    console.error('❌ Failed to upgrade/downgrade subscription:', error);

    if (error instanceof HttpError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message,
      });
    }

    return res.status(500).json({
      success: false,
      message: 'Internal server error while upgrading/downgrading subscription',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

/**
 * Get subscription analytics for user
 */
export const handleGetSubscriptionAnalytics = async (req: Request, res: Response, context: any) => {
  try {
    // Check authentication
    if (!context.user) {
      throw new HttpError(401, 'User not authenticated');
    }

    const userId = context.user.id;

    // Get all user subscriptions
    const allSubscriptions = await context.entities.UserSubscription.findMany({
      where: { userId },
      include: {
        subscription: true,
      },
      orderBy: { createdAt: 'asc' },
    });

    // Calculate analytics
    const totalSubscriptions = allSubscriptions.length;
    const activeSubscriptions = allSubscriptions.filter(sub => sub.status === 'active').length;
    const cancelledSubscriptions = allSubscriptions.filter(sub => sub.status === 'cancelled').length;
    const expiredSubscriptions = allSubscriptions.filter(sub => sub.status === 'expired').length;

    const totalSpent = allSubscriptions.reduce((sum, sub) => sum + sub.subscription.price, 0);
    const totalCreditsAllocated = allSubscriptions.reduce((sum, sub) => sum + sub.creditsAllocated, 0);

    // Get subscription timeline
    const subscriptionTimeline = allSubscriptions.map(sub => ({
      id: sub.id,
      subscriptionName: sub.subscription.name,
      status: sub.status,
      startDate: sub.startDate,
      endDate: sub.endDate,
      price: sub.subscription.price,
      creditsAllocated: sub.creditsAllocated,
    }));

    // Calculate average subscription duration
    const completedSubscriptions = allSubscriptions.filter(sub => sub.endDate && sub.startDate);
    const averageDuration = completedSubscriptions.length > 0
      ? completedSubscriptions.reduce((sum, sub) => {
          const duration = new Date(sub.endDate!).getTime() - new Date(sub.startDate).getTime();
          return sum + duration;
        }, 0) / completedSubscriptions.length / (1000 * 60 * 60 * 24) // Convert to days
      : 0;

    return res.status(200).json({
      success: true,
      message: 'Subscription analytics retrieved successfully',
      data: {
        summary: {
          totalSubscriptions,
          activeSubscriptions,
          cancelledSubscriptions,
          expiredSubscriptions,
          totalSpent,
          totalSpentFormatted: `$${(totalSpent / 100).toFixed(2)}`,
          totalCreditsAllocated,
          averageDurationDays: Math.round(averageDuration),
        },
        timeline: subscriptionTimeline,
        generatedAt: new Date(),
      },
    });
  } catch (error) {
    console.error('❌ Failed to get subscription analytics:', error);

    if (error instanceof HttpError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message,
      });
    }

    return res.status(500).json({
      success: false,
      message: 'Internal server error while retrieving subscription analytics',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

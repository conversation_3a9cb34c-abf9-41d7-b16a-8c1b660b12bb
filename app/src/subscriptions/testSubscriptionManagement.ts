import { type Request, type Response } from 'express';
import { HttpError } from 'wasp/server';

/**
 * Test subscription management functionality
 * Comprehensive test for subscription lifecycle operations
 */
export const handleTestSubscriptionManagement = async (req: Request, res: Response, context: any) => {
  try {
    console.log('🧪 Starting subscription management test...');

    const testResults: any[] = [];

    // Test 1: Get available subscriptions
    try {
      console.log('📋 Test 1: Getting available subscriptions...');
      
      const subscriptions = await context.entities.Subscription.findMany({
        where: { isActive: true },
        take: 5, // Limit for testing
      });

      testResults.push({
        test: 'get_available_subscriptions',
        success: true,
        data: {
          subscriptionCount: subscriptions.length,
          subscriptions: subscriptions.map(sub => ({
            id: sub.id,
            name: sub.name,
            price: sub.price,
            priceFormatted: `$${(sub.price / 100).toFixed(2)}`,
            interval: sub.interval,
            creditsIncluded: sub.creditsIncluded,
          })),
        },
      });
    } catch (error) {
      testResults.push({
        test: 'get_available_subscriptions',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }

    // Test 2: Create test user and subscription
    try {
      console.log('👤 Test 2: Creating test user and subscription...');
      
      // Create test user
      const testUser = await context.entities.User.create({
        data: {
          email: `test-subscription-${Date.now()}@example.com`,
          credits: 50,
          queues: 1,
        },
      });

      // Get a subscription to use for testing
      const testSubscription = await context.entities.Subscription.findFirst({
        where: { isActive: true },
      });

      if (!testSubscription) {
        throw new Error('No active subscription found for testing');
      }

      // Create user subscription
      const userSubscription = await context.entities.UserSubscription.create({
        data: {
          userId: testUser.id,
          subscriptionId: testSubscription.id,
          status: 'active',
          startDate: new Date(),
          endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
          creditsAllocated: testSubscription.creditsIncluded,
        },
      });

      testResults.push({
        test: 'create_test_user_subscription',
        success: true,
        data: {
          userId: testUser.id,
          userEmail: testUser.email,
          subscriptionId: userSubscription.id,
          subscriptionName: testSubscription.name,
          status: userSubscription.status,
          creditsAllocated: userSubscription.creditsAllocated,
        },
      });

      // Test 3: Test subscription status updates
      console.log('🔄 Test 3: Testing subscription status updates...');
      
      try {
        // Update subscription status to cancelled
        const updatedSubscription = await context.entities.UserSubscription.update({
          where: { id: userSubscription.id },
          data: { status: 'cancelled' },
        });

        testResults.push({
          test: 'subscription_status_update',
          success: true,
          data: {
            subscriptionId: updatedSubscription.id,
            previousStatus: 'active',
            newStatus: updatedSubscription.status,
            updatedAt: updatedSubscription.updatedAt,
          },
        });
      } catch (error) {
        testResults.push({
          test: 'subscription_status_update',
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }

      // Test 4: Test subscription analytics calculation
      console.log('📊 Test 4: Testing subscription analytics...');
      
      try {
        // Get user subscriptions for analytics
        const userSubscriptions = await context.entities.UserSubscription.findMany({
          where: { userId: testUser.id },
          include: { subscription: true },
        });

        const analytics = {
          totalSubscriptions: userSubscriptions.length,
          activeSubscriptions: userSubscriptions.filter(sub => sub.status === 'active').length,
          cancelledSubscriptions: userSubscriptions.filter(sub => sub.status === 'cancelled').length,
          totalCreditsAllocated: userSubscriptions.reduce((sum, sub) => sum + sub.creditsAllocated, 0),
          totalSpent: userSubscriptions.reduce((sum, sub) => sum + sub.subscription.price, 0),
        };

        testResults.push({
          test: 'subscription_analytics',
          success: true,
          data: {
            analytics,
            formattedTotalSpent: `$${(analytics.totalSpent / 100).toFixed(2)}`,
          },
        });
      } catch (error) {
        testResults.push({
          test: 'subscription_analytics',
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }

      // Test 5: Test subscription upgrade simulation
      console.log('⬆️ Test 5: Testing subscription upgrade simulation...');
      
      try {
        // Get a different subscription for upgrade test
        const upgradeSubscription = await context.entities.Subscription.findFirst({
          where: { 
            isActive: true,
            id: { not: testSubscription.id },
          },
        });

        if (upgradeSubscription) {
          // Simulate upgrade by creating new subscription and cancelling old one
          const newUserSubscription = await context.entities.UserSubscription.create({
            data: {
              userId: testUser.id,
              subscriptionId: upgradeSubscription.id,
              status: 'active',
              startDate: new Date(),
              endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
              creditsAllocated: upgradeSubscription.creditsIncluded,
            },
          });

          const upgradeData = {
            previousSubscription: {
              id: testSubscription.id,
              name: testSubscription.name,
              price: testSubscription.price,
            },
            newSubscription: {
              id: upgradeSubscription.id,
              name: upgradeSubscription.name,
              price: upgradeSubscription.price,
            },
            isUpgrade: upgradeSubscription.price > testSubscription.price,
            priceDifference: upgradeSubscription.price - testSubscription.price,
          };

          testResults.push({
            test: 'subscription_upgrade_simulation',
            success: true,
            data: upgradeData,
          });
        } else {
          testResults.push({
            test: 'subscription_upgrade_simulation',
            success: false,
            error: 'No alternative subscription found for upgrade test',
          });
        }
      } catch (error) {
        testResults.push({
          test: 'subscription_upgrade_simulation',
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }

      // Test 6: Test subscription history retrieval
      console.log('📜 Test 6: Testing subscription history retrieval...');
      
      try {
        const subscriptionHistory = await context.entities.UserSubscription.findMany({
          where: { userId: testUser.id },
          include: { subscription: true },
          orderBy: { createdAt: 'desc' },
        });

        const formattedHistory = subscriptionHistory.map(sub => ({
          id: sub.id,
          subscriptionName: sub.subscription.name,
          status: sub.status,
          startDate: sub.startDate,
          endDate: sub.endDate,
          creditsAllocated: sub.creditsAllocated,
          price: sub.subscription.price,
        }));

        testResults.push({
          test: 'subscription_history_retrieval',
          success: true,
          data: {
            historyCount: formattedHistory.length,
            history: formattedHistory,
          },
        });
      } catch (error) {
        testResults.push({
          test: 'subscription_history_retrieval',
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }

    } catch (error) {
      testResults.push({
        test: 'test_setup_error',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }

    // Calculate summary
    const successCount = testResults.filter(r => r.success).length;
    const totalCount = testResults.length;

    return res.status(200).json({
      success: true,
      message: 'Subscription management test completed',
      data: {
        summary: {
          passed: successCount,
          total: totalCount,
          successRate: `${Math.round((successCount / totalCount) * 100)}%`,
        },
        results: testResults,
        timestamp: new Date(),
      },
    });

  } catch (error) {
    console.error('❌ Subscription management test failed:', error);
    
    return res.status(500).json({
      success: false,
      message: 'Internal server error during subscription management test',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

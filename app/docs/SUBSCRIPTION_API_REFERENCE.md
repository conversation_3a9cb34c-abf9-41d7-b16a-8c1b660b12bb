# Dalti Subscription System - API Reference

## 📋 Table of Contents

1. [Authentication](#authentication)
2. [Base URLs](#base-urls)
3. [Response Format](#response-format)
4. [Error Handling](#error-handling)
5. [Subscription Management APIs](#subscription-management-apis)
6. [User Management APIs](#user-management-apis)
7. [Checkout & Payment APIs](#checkout--payment-apis)
8. [Webhook APIs](#webhook-apis)
9. [Customer Portal APIs](#customer-portal-apis)
10. [Analytics & Monitoring APIs](#analytics--monitoring-apis)

## 🔐 Authentication

### JWT Token Authentication (Recommended)
```http
Authorization: Bearer {jwt-token}
```

### API Key Authentication
```http
X-API-Key: {your-api-key}
```

## 🌐 Base URLs

| Environment | URL |
|-------------|-----|
| Production | `https://api.dalti.app` |
| Staging | `https://api-staging.dalti.app` |
| Development | `http://localhost:5400` |

## 📄 Response Format

### Success Response
```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": {
    // Response data here
  }
}
```

### Error Response
```json
{
  "success": false,
  "message": "Human-readable error message",
  "error": "Technical error details",
  "errors": {
    "field": ["Validation error message"]
  }
}
```

## ❌ Error Handling

### HTTP Status Codes
| Code | Meaning | Description |
|------|---------|-------------|
| 200 | OK | Request successful |
| 201 | Created | Resource created |
| 400 | Bad Request | Invalid request data |
| 401 | Unauthorized | Authentication required |
| 403 | Forbidden | Insufficient permissions |
| 404 | Not Found | Resource not found |
| 429 | Too Many Requests | Rate limit exceeded |
| 500 | Internal Server Error | Server error |

## 💳 Subscription Management APIs

### Get Available Subscriptions
```http
GET /api/external/subscriptions
```

**Headers:**
```
Authorization: Bearer {jwt-token}
```

**Response:**
```json
{
  "success": true,
  "message": "Subscriptions retrieved successfully",
  "data": {
    "subscriptions": [
      {
        "id": "free-plan",
        "name": "Free Plan",
        "description": "Perfect for getting started",
        "price": 0,
        "priceFormatted": "$0.00",
        "interval": "monthly",
        "creditsIncluded": 100,
        "features": [
          "Basic appointment management",
          "Up to 100 credits/month",
          "Email support"
        ],
        "isActive": true,
        "isPopular": false
      },
      {
        "id": "pro-plan",
        "name": "Pro Plan",
        "description": "Perfect for growing businesses",
        "price": 2000,
        "priceFormatted": "$20.00",
        "interval": "monthly",
        "creditsIncluded": 2000,
        "features": [
          "Full appointment management",
          "Up to 2000 credits/month",
          "24/7 priority support",
          "Advanced analytics"
        ],
        "isActive": true,
        "isPopular": true
      }
    ],
    "total": 2
  }
}
```

### Get User Subscription Status
```http
GET /api/auth/providers/subscription-status
```

**Headers:**
```
Authorization: Bearer {jwt-token}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "hasActiveSubscription": true,
    "subscription": {
      "id": "sub_123456",
      "subscriptionId": "pro-plan",
      "name": "Pro Plan",
      "status": "active",
      "startDate": "2025-01-01T00:00:00.000Z",
      "endDate": "2025-02-01T00:00:00.000Z",
      "daysRemaining": 15,
      "creditsAllocated": 2000,
      "price": 2000,
      "priceFormatted": "$20.00",
      "interval": "monthly",
      "features": [
        "Full appointment management",
        "Up to 2000 credits/month",
        "24/7 priority support"
      ]
    },
    "credits": {
      "current": 1750,
      "allocated": 2000,
      "used": 250,
      "percentage": 87.5
    }
  }
}
```

### Get Subscription History
```http
GET /api/auth/subscriptions/history
```

**Headers:**
```
Authorization: Bearer {jwt-token}
```

**Query Parameters:**
| Parameter | Type | Description |
|-----------|------|-------------|
| limit | integer | Number of records (default: 10) |
| offset | integer | Pagination offset (default: 0) |

**Response:**
```json
{
  "success": true,
  "data": {
    "subscriptions": [
      {
        "id": "sub_123456",
        "subscriptionName": "Pro Plan",
        "status": "active",
        "startDate": "2025-01-01T00:00:00.000Z",
        "endDate": "2025-02-01T00:00:00.000Z",
        "price": 2000,
        "priceFormatted": "$20.00",
        "creditsAllocated": 2000,
        "createdAt": "2025-01-01T00:00:00.000Z"
      }
    ],
    "total": 1,
    "pagination": {
      "limit": 10,
      "offset": 0,
      "hasMore": false
    }
  }
}
```

### Cancel Subscription
```http
POST /api/auth/subscriptions/cancel
```

**Headers:**
```
Authorization: Bearer {jwt-token}
Content-Type: application/json
```

**Request Body:**
```json
{
  "subscriptionId": "sub_123456",
  "reason": "no_longer_needed",
  "feedback": "Service was great, but scaling down operations"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Subscription cancelled successfully",
  "data": {
    "subscriptionId": "sub_123456",
    "status": "cancelled",
    "cancelledAt": "2025-07-22T10:00:00.000Z",
    "effectiveDate": "2025-02-01T00:00:00.000Z"
  }
}
```

## 👤 User Management APIs

### Get User Credits
```http
GET /api/users/credits
```

**Headers:**
```
Authorization: Bearer {jwt-token}
```

**Query Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| userId | string | Yes | User ID to get credits for |

**Response:**
```json
{
  "success": true,
  "data": {
    "userId": "user_123456",
    "credits": 1750,
    "lastUpdated": "2025-07-22T08:00:00.000Z",
    "creditHistory": [
      {
        "id": "credit_001",
        "date": "2025-07-22T08:00:00.000Z",
        "change": -50,
        "reason": "Appointment booking",
        "balance": 1750,
        "description": "Customer appointment with Dr. Smith"
      },
      {
        "id": "credit_002",
        "date": "2025-07-01T00:00:00.000Z",
        "change": 2000,
        "reason": "Monthly subscription renewal",
        "balance": 1800,
        "description": "Pro Plan monthly credits"
      }
    ]
  }
}
```

### Update User Credits
```http
PUT /api/users/credits
```

**Headers:**
```
Authorization: Bearer {jwt-token}
Content-Type: application/json
```

**Request Body:**
```json
{
  "userId": "user_123456",
  "credits": 1500,
  "reason": "Manual adjustment",
  "description": "Admin credit adjustment"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Credits updated successfully",
  "data": {
    "userId": "user_123456",
    "previousCredits": 1750,
    "newCredits": 1500,
    "change": -250,
    "updatedAt": "2025-07-22T10:00:00.000Z"
  }
}
```

## 💰 Checkout & Payment APIs

### Create Checkout Session
```http
POST /api/external/checkout
```

**Headers:**
```
Authorization: Bearer {jwt-token}
Content-Type: application/json
```

**Request Body:**
```json
{
  "planIdentifier": "pro-plan",
  "returnUrl": "https://yoursite.com/subscription/success",
  "cancelUrl": "https://yoursite.com/subscription/cancel",
  "customerInfo": {
    "email": "<EMAIL>",
    "name": "Dr. John Smith"
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "Checkout session created successfully",
  "data": {
    "sessionId": "cs_1234567890abcdef",
    "sessionUrl": "https://checkout.dalti.app/session/cs_1234567890abcdef",
    "planIdentifier": "pro-plan",
    "returnUrl": "https://yoursite.com/subscription/success",
    "cancelUrl": "https://yoursite.com/subscription/cancel",
    "expiresAt": "2025-07-22T12:00:00.000Z",
    "createdAt": "2025-07-22T10:00:00.000Z"
  }
}
```

### Get Checkout Session Status
```http
GET /api/external/checkout/status/{sessionId}
```

**Headers:**
```
Authorization: Bearer {jwt-token}
```

**Path Parameters:**
| Parameter | Type | Description |
|-----------|------|-------------|
| sessionId | string | Checkout session ID |

**Response:**
```json
{
  "success": true,
  "data": {
    "sessionId": "cs_1234567890abcdef",
    "status": "completed",
    "planIdentifier": "pro-plan",
    "customerEmail": "<EMAIL>",
    "completedAt": "2025-07-22T10:30:00.000Z",
    "subscription": {
      "id": "sub_123456",
      "name": "Pro Plan",
      "status": "active",
      "startDate": "2025-07-22T10:30:00.000Z",
      "endDate": "2025-08-22T10:30:00.000Z"
    }
  }
}
```

## 🔗 Webhook APIs

### Register Webhook Callback
```http
POST /api/webhooks/callbacks
```

**Headers:**
```
Authorization: Bearer {jwt-token}
Content-Type: application/json
```

**Request Body:**
```json
{
  "url": "https://yoursite.com/webhooks/dalti",
  "secret": "your-webhook-secret-key",
  "events": [
    "subscription.created",
    "subscription.updated",
    "subscription.cancelled",
    "payment.succeeded",
    "payment.failed",
    "credits.updated"
  ],
  "description": "Provider dashboard webhook"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Webhook callback registered successfully",
  "data": {
    "id": "webhook_123456",
    "url": "https://yoursite.com/webhooks/dalti",
    "events": [
      "subscription.created",
      "subscription.updated",
      "subscription.cancelled",
      "payment.succeeded",
      "payment.failed",
      "credits.updated"
    ],
    "status": "active",
    "createdAt": "2025-07-22T10:00:00.000Z"
  }
}
```

### Get Webhook Callbacks
```http
GET /api/webhooks/callbacks
```

**Headers:**
```
Authorization: Bearer {jwt-token}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "webhooks": [
      {
        "id": "webhook_123456",
        "url": "https://yoursite.com/webhooks/dalti",
        "events": [
          "subscription.created",
          "payment.succeeded"
        ],
        "status": "active",
        "lastTriggered": "2025-07-22T09:30:00.000Z",
        "createdAt": "2025-07-22T08:00:00.000Z"
      }
    ],
    "total": 1
  }
}
```

### Delete Webhook Callback
```http
DELETE /api/webhooks/callbacks/{webhookId}
```

**Headers:**
```
Authorization: Bearer {jwt-token}
```

**Path Parameters:**
| Parameter | Type | Description |
|-----------|------|-------------|
| webhookId | string | Webhook ID to delete |

**Response:**
```json
{
  "success": true,
  "message": "Webhook callback deleted successfully",
  "data": {
    "webhookId": "webhook_123456",
    "deletedAt": "2025-07-22T10:00:00.000Z"
  }
}
```

### Webhook Event Format

When events occur, webhooks will receive POST requests with this format:

**Headers:**
```
Content-Type: application/json
X-Webhook-Signature: sha256=<signature>
```

**Body:**
```json
{
  "eventType": "subscription.created",
  "eventId": "evt_123456789",
  "timestamp": "2025-07-22T10:00:00.000Z",
  "data": {
    "subscription": {
      "id": "sub_123456",
      "userId": "user_123456",
      "planId": "pro-plan",
      "status": "active",
      "startDate": "2025-07-22T10:00:00.000Z",
      "endDate": "2025-08-22T10:00:00.000Z"
    },
    "user": {
      "id": "user_123456",
      "email": "<EMAIL>"
    }
  }
}
```

### Webhook Event Types

| Event Type | Description | When Triggered |
|------------|-------------|----------------|
| `subscription.created` | New subscription activated | After successful payment |
| `subscription.updated` | Subscription modified | Plan change, billing update |
| `subscription.cancelled` | Subscription cancelled | User or admin cancellation |
| `subscription.expired` | Subscription expired | End of billing period |
| `payment.succeeded` | Payment completed | Successful charge |
| `payment.failed` | Payment failed | Failed charge attempt |
| `credits.updated` | User credits changed | Credit addition/deduction |

## 🎨 Customer Portal APIs

### Generate Portal Session
```http
POST /api/portal/sessions
```

**Headers:**
```
Authorization: Bearer {jwt-token}
Content-Type: application/json
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "returnUrl": "https://yoursite.com/dashboard",
  "features": [
    "subscription_management",
    "billing_history",
    "payment_methods",
    "usage_analytics"
  ],
  "theme": {
    "primaryColor": "#007bff",
    "backgroundColor": "#ffffff",
    "fontFamily": "Inter, sans-serif",
    "logoUrl": "https://yoursite.com/logo.png"
  },
  "sessionDuration": 3600
}
```

**Response:**
```json
{
  "success": true,
  "message": "Customer portal session created successfully",
  "data": {
    "sessionId": "portal_123456789",
    "portalUrl": "https://portal.dalti.app?session=portal_123456789&features=subscription_management,billing_history&primary_color=%23007bff",
    "userId": "user_123456",
    "userEmail": "<EMAIL>",
    "features": [
      "subscription_management",
      "billing_history",
      "payment_methods"
    ],
    "expiresAt": "2025-07-22T11:00:00.000Z",
    "returnUrl": "https://yoursite.com/dashboard",
    "createdAt": "2025-07-22T10:00:00.000Z"
  }
}
```

### Get Portal Session Details
```http
GET /api/portal/sessions/{sessionId}
```

**Headers:**
```
Authorization: Bearer {jwt-token}
```

**Path Parameters:**
| Parameter | Type | Description |
|-----------|------|-------------|
| sessionId | string | Portal session ID |

**Response:**
```json
{
  "success": true,
  "data": {
    "sessionId": "portal_123456789",
    "userId": "user_123456",
    "features": [
      "subscription_management",
      "billing_history"
    ],
    "expiresAt": "2025-07-22T11:00:00.000Z",
    "createdAt": "2025-07-22T10:00:00.000Z",
    "user": {
      "id": "user_123456",
      "email": "<EMAIL>",
      "credits": 1750,
      "subscriptions": [
        {
          "id": "sub_123456",
          "name": "Pro Plan",
          "status": "active",
          "startDate": "2025-07-22T10:00:00.000Z",
          "endDate": "2025-08-22T10:00:00.000Z"
        }
      ]
    },
    "timeRemaining": 3540
  }
}
```

## 📊 Analytics & Monitoring APIs

### Get System Metrics
```http
GET /api/monitoring/metrics
```

**Headers:**
```
Authorization: Bearer {jwt-token}
```

**Query Parameters:**
| Parameter | Type | Description |
|-----------|------|-------------|
| startDate | string | Start date (ISO 8601) |
| endDate | string | End date (ISO 8601) |
| granularity | string | hour, day, week, month |

**Response:**
```json
{
  "success": true,
  "data": {
    "current": {
      "timestamp": "2025-07-22T10:00:00.000Z",
      "performance": {
        "responseTime": 125.5,
        "throughput": 850,
        "errorRate": 1.2,
        "uptime": 99.95
      },
      "business": {
        "activeSubscriptions": 3,
        "newSubscriptions": 8,
        "revenue": 12500,
        "conversionRate": 8.5
      },
      "usage": {
        "apiCalls": 9500,
        "uniqueUsers": 13,
        "checkoutSessions": 45,
        "webhookEvents": 120
      }
    },
    "trends": {
      "responseTime": {
        "current": 125.5,
        "change": -15.2,
        "trend": "down"
      },
      "activeSubscriptions": {
        "current": 3,
        "change": 1,
        "trend": "up"
      }
    }
  }
}
```

### Get Business Analytics
```http
GET /api/monitoring/business
```

**Headers:**
```
Authorization: Bearer {jwt-token}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "subscriptions": {
      "total": 5,
      "active": 3,
      "cancelled": 2,
      "churnRate": 40
    },
    "revenue": {
      "totalRevenue": 125000,
      "monthlyRecurringRevenue": 15000,
      "averageRevenuePerUser": 25.50,
      "revenueGrowthRate": 12.5
    },
    "users": {
      "total": 13,
      "newThisMonth": 1,
      "activeThisMonth": 10,
      "retentionRate": 85.2
    },
    "conversions": {
      "overallRate": 8.5,
      "byPlan": {
        "Free Plan": 15.2,
        "Hobby Plan": 8.1,
        "Pro Plan": 5.8
      }
    }
  }
}
```

### Get System Health
```http
GET /api/monitoring/health
```

**Headers:**
```
Authorization: Bearer {jwt-token}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "overall": {
      "status": "healthy",
      "score": 100,
      "lastChecked": "2025-07-22T10:00:00.000Z"
    },
    "services": {
      "database": {
        "status": "healthy",
        "responseTime": 25,
        "connections": 22
      },
      "api": {
        "status": "healthy",
        "responseTime": 145,
        "throughput": 850,
        "errorRate": 1.2
      },
      "externalServices": {
        "lemonSqueezy": {
          "status": "healthy",
          "responseTime": 150
        },
        "emailService": {
          "status": "healthy",
          "responseTime": 80
        }
      }
    },
    "alerts": {
      "active": 1,
      "triggered": 0
    }
  }
}
```

## 🔧 Server-to-Server APIs

### Create API Key
```http
POST /api/server/api-keys
```

**Headers:**
```
X-API-Key: {admin-api-key}
Content-Type: application/json
```

**Request Body:**
```json
{
  "name": "Provider Dashboard Integration",
  "description": "API key for provider dashboard website",
  "permissions": [
    "subscriptions:read",
    "subscriptions:write",
    "users:read",
    "webhooks:manage"
  ],
  "expiresAt": "2026-07-22T10:00:00.000Z"
}
```

**Response:**
```json
{
  "success": true,
  "message": "API key created successfully",
  "data": {
    "id": "key_123456789",
    "name": "Provider Dashboard Integration",
    "apiKey": "sk_live_1234567890abcdef",
    "permissions": [
      "subscriptions:read",
      "subscriptions:write",
      "users:read",
      "webhooks:manage"
    ],
    "createdAt": "2025-07-22T10:00:00.000Z",
    "expiresAt": "2026-07-22T10:00:00.000Z"
  }
}
```

### Get API Keys
```http
GET /api/server/api-keys
```

**Headers:**
```
X-API-Key: {admin-api-key}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "apiKeys": [
      {
        "id": "key_123456789",
        "name": "Provider Dashboard Integration",
        "permissions": [
          "subscriptions:read",
          "users:read"
        ],
        "lastUsed": "2025-07-22T09:30:00.000Z",
        "createdAt": "2025-07-22T08:00:00.000Z",
        "expiresAt": "2026-07-22T10:00:00.000Z",
        "status": "active"
      }
    ],
    "total": 1
  }
}
```

## 🚀 Rate Limits

### Rate Limit Headers

All API responses include rate limit information:

```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: **********
```

### Rate Limits by Endpoint Type

| Endpoint Type | Limit | Window |
|---------------|-------|--------|
| Authentication | 100 requests | 15 minutes |
| Subscription Management | 1000 requests | 1 hour |
| Checkout & Payment | 500 requests | 1 hour |
| Webhooks | 100 requests | 1 hour |
| Analytics | 200 requests | 1 hour |

### Rate Limit Exceeded Response

```json
{
  "success": false,
  "message": "Rate limit exceeded",
  "error": "Too many requests. Please try again later.",
  "retryAfter": 3600
}
```

## 🔒 Security

### API Key Security
- Store API keys securely (environment variables)
- Never expose API keys in client-side code
- Rotate API keys regularly
- Use least privilege permissions

### JWT Token Security
- Tokens expire after 24 hours
- Implement token refresh mechanism
- Store tokens securely (httpOnly cookies preferred)
- Validate tokens on every request

### Webhook Security
- Verify webhook signatures using HMAC-SHA256
- Use HTTPS endpoints only
- Implement idempotency for webhook handlers
- Store webhook secrets securely

### Signature Verification Example

```javascript
const crypto = require('crypto');

function verifyWebhookSignature(payload, signature, secret) {
  const expectedSignature = crypto
    .createHmac('sha256', secret)
    .update(payload)
    .digest('hex');

  return crypto.timingSafeEqual(
    Buffer.from(signature, 'hex'),
    Buffer.from(expectedSignature, 'hex')
  );
}
```

## 📝 API Versioning

### Current Version
- **API Version**: v1
- **Base Path**: All endpoints are prefixed with `/api/`
- **Versioning Strategy**: URL path versioning (future: `/api/v2/`)

### Backward Compatibility
- Breaking changes will introduce new API versions
- Previous versions supported for 12 months
- Deprecation notices provided 90 days in advance

## 🧪 Testing

### Test Environment
- **Base URL**: `https://api-staging.dalti.app`
- **Test API Keys**: Contact support for test credentials
- **Test Data**: Sandbox environment with sample data

### Example API Test

```bash
# Test subscription status endpoint
curl -X GET "https://api-staging.dalti.app/api/auth/providers/subscription-status" \
  -H "Authorization: Bearer your-test-jwt-token" \
  -H "Content-Type: application/json"

# Test available subscriptions
curl -X GET "https://api-staging.dalti.app/api/external/subscriptions" \
  -H "X-API-Key: your-test-api-key" \
  -H "Content-Type: application/json"
```

## 📞 Support

### API Support
- **Email**: <EMAIL>
- **Response Time**: < 24 hours
- **Documentation**: https://docs.dalti.app

### Status & Monitoring
- **API Status**: https://status.dalti.app
- **Uptime SLA**: 99.9%
- **Response Time SLA**: < 200ms average

### Contact Information
- **Technical Issues**: <EMAIL>
- **Integration Help**: <EMAIL>
- **Security Issues**: <EMAIL>

---

## 🎯 Quick Start

1. **Get API credentials** from support team
2. **Test connectivity** with staging environment
3. **Implement authentication** (JWT or API key)
4. **Start with subscription status** endpoint
5. **Add checkout integration** for plan upgrades
6. **Set up webhooks** for real-time updates

**The API is production-ready and fully documented!** 🚀
```

# Dalti Subscription API - Quick Reference Card

## 🔐 Authentication
```bash
# JWT Token (Recommended)
Authorization: Bearer {jwt-token}

# API Key (Server-to-Server)
X-API-Key: {your-api-key}
```

## 🌐 Base URLs
- **Production**: `https://api.dalti.app`
- **Staging**: `https://api-staging.dalti.app`
- **Development**: `http://localhost:5400`

## 💳 Core Subscription APIs

### Get Available Plans
```bash
GET /api/external/subscriptions
Authorization: Bearer {jwt-token}
```

### Get Subscription Status
```bash
GET /api/auth/providers/subscription-status
Authorization: Bearer {jwt-token}
```

### Create Checkout Session
```bash
POST /api/external/checkout
Authorization: Bearer {jwt-token}
Content-Type: application/json

{
  "planIdentifier": "pro-plan",
  "returnUrl": "https://yoursite.com/success",
  "cancelUrl": "https://yoursite.com/cancel",
  "customerInfo": {
    "email": "<EMAIL>",
    "name": "Provider Name"
  }
}
```

### Get Checkout Status
```bash
GET /api/external/checkout/status/{sessionId}
Authorization: Bearer {jwt-token}
```

### Cancel Subscription
```bash
POST /api/auth/subscriptions/cancel
Authorization: Bearer {jwt-token}
Content-Type: application/json

{
  "subscriptionId": "sub_123456",
  "reason": "no_longer_needed",
  "feedback": "Optional feedback"
}
```

## 👤 User & Credits APIs

### Get User Credits
```bash
GET /api/users/credits?userId={user-id}
Authorization: Bearer {jwt-token}
```

### Update User Credits
```bash
PUT /api/users/credits
Authorization: Bearer {jwt-token}
Content-Type: application/json

{
  "userId": "user_123456",
  "credits": 1500,
  "reason": "Manual adjustment"
}
```

### Get Subscription History
```bash
GET /api/auth/subscriptions/history?limit=10&offset=0
Authorization: Bearer {jwt-token}
```

## 🔗 Webhook APIs

### Register Webhook
```bash
POST /api/webhooks/callbacks
Authorization: Bearer {jwt-token}
Content-Type: application/json

{
  "url": "https://yoursite.com/webhooks/dalti",
  "secret": "your-webhook-secret",
  "events": [
    "subscription.created",
    "subscription.cancelled",
    "payment.succeeded",
    "credits.updated"
  ]
}
```

### Get Webhooks
```bash
GET /api/webhooks/callbacks
Authorization: Bearer {jwt-token}
```

### Delete Webhook
```bash
DELETE /api/webhooks/callbacks/{webhookId}
Authorization: Bearer {jwt-token}
```

## 🎨 Customer Portal APIs

### Generate Portal Session
```bash
POST /api/portal/sessions
Authorization: Bearer {jwt-token}
Content-Type: application/json

{
  "email": "<EMAIL>",
  "returnUrl": "https://yoursite.com/dashboard",
  "features": ["subscription_management", "billing_history"],
  "theme": {
    "primaryColor": "#007bff",
    "logoUrl": "https://yoursite.com/logo.png"
  }
}
```

### Get Portal Session
```bash
GET /api/portal/sessions/{sessionId}
Authorization: Bearer {jwt-token}
```

## 📊 Analytics APIs

### Get System Metrics
```bash
GET /api/monitoring/metrics?granularity=day
Authorization: Bearer {jwt-token}
```

### Get Business Analytics
```bash
GET /api/monitoring/business
Authorization: Bearer {jwt-token}
```

### Get System Health
```bash
GET /api/monitoring/health
Authorization: Bearer {jwt-token}
```

## 🔧 Server APIs

### Create API Key
```bash
POST /api/server/api-keys
X-API-Key: {admin-api-key}
Content-Type: application/json

{
  "name": "Integration Key",
  "permissions": ["subscriptions:read", "users:read"]
}
```

### Get API Keys
```bash
GET /api/server/api-keys
X-API-Key: {admin-api-key}
```

## 📄 Response Format

### Success Response
```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": { /* response data */ }
}
```

### Error Response
```json
{
  "success": false,
  "message": "Human-readable error message",
  "error": "Technical error details"
}
```

## ❌ HTTP Status Codes
- **200** - Success
- **201** - Created
- **400** - Bad Request
- **401** - Unauthorized
- **403** - Forbidden
- **404** - Not Found
- **429** - Rate Limited
- **500** - Server Error

## 🔗 Webhook Events
- `subscription.created` - New subscription activated
- `subscription.updated` - Subscription modified
- `subscription.cancelled` - Subscription cancelled
- `payment.succeeded` - Payment completed
- `payment.failed` - Payment failed
- `credits.updated` - User credits changed

## 🚨 Rate Limits
- **Authentication**: 100 req/15min
- **Subscriptions**: 1000 req/hour
- **Checkout**: 500 req/hour
- **Webhooks**: 100 req/hour
- **Analytics**: 200 req/hour

## 🔒 Security Headers

### Rate Limit Headers
```
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: **********
```

### Webhook Signature
```
X-Webhook-Signature: sha256=<signature>
```

## 🧪 Testing Examples

### Test Subscription Status
```bash
curl -X GET "https://api-staging.dalti.app/api/auth/providers/subscription-status" \
  -H "Authorization: Bearer your-test-jwt-token"
```

### Test Available Plans
```bash
curl -X GET "https://api-staging.dalti.app/api/external/subscriptions" \
  -H "X-API-Key: your-test-api-key"
```

### Test Checkout Creation
```bash
curl -X POST "https://api-staging.dalti.app/api/external/checkout" \
  -H "Authorization: Bearer your-test-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "planIdentifier": "pro-plan",
    "returnUrl": "https://test.com/success",
    "cancelUrl": "https://test.com/cancel",
    "customerInfo": {
      "email": "<EMAIL>",
      "name": "Test User"
    }
  }'
```

## 📞 Support
- **API Support**: <EMAIL>
- **Status Page**: https://status.dalti.app
- **Documentation**: [Full API Reference](./SUBSCRIPTION_API_REFERENCE.md)

---

**Need more details?** Check the [complete API documentation](./SUBSCRIPTION_API_REFERENCE.md)

# Subscription System API Documentation

## Authentication

All authenticated endpoints require a Bearer token in the Authorization header:

```http
Authorization: Bearer <session_token>
```

## Subscription Plans

### Get All Subscription Plans

Retrieve all available subscription plans.

```http
GET /api/subscriptions
```

**Query Parameters:**

| Parameter | Type | Description | Default |
|-----------|------|-------------|---------|
| `isActive` | boolean | Filter by active status | - |
| `interval` | string | Filter by interval (`monthly`, `yearly`, `one-time`) | - |

**Response:**

```json
{
  "success": true,
  "message": "Subscriptions retrieved successfully",
  "data": [
    {
      "id": "550e8400-e29b-41d4-a716-446655440000",
      "name": "Professional Plan",
      "description": "Advanced features for professionals",
      "price": 29.99,
      "duration": 30,
      "interval": "monthly",
      "creditsIncluded": 1000,
      "features": [
        "unlimited_credits",
        "priority_support",
        "api_access",
        "multiple_queues"
      ],
      "isActive": true,
      "createdAt": "2024-01-15T10:00:00Z",
      "updatedAt": "2024-01-15T10:00:00Z"
    }
  ]
}
```

**Status Codes:**
- `200` - Success
- `500` - Internal server error

---

### Subscribe to Plan

Create a new subscription for the authenticated user.

```http
POST /api/auth/subscriptions/subscribe
```

**Request Body:**

```json
{
  "subscriptionId": "550e8400-e29b-41d4-a716-446655440000"
}
```

**Response:**

```json
{
  "success": true,
  "message": "Subscription created successfully",
  "data": {
    "userSubscriptionId": "660e8400-e29b-41d4-a716-446655440001",
    "subscriptionId": "550e8400-e29b-41d4-a716-446655440000",
    "status": "pending",
    "startDate": "2024-01-15T10:00:00Z",
    "endDate": "2024-02-14T10:00:00Z",
    "creditsAllocated": 1000
  }
}
```

**Status Codes:**
- `201` - Subscription created
- `400` - Invalid subscription ID
- `401` - Unauthorized
- `409` - User already has active subscription
- `500` - Internal server error

---

## User Subscriptions

### Get User Subscription (Enhanced)

Retrieve comprehensive subscription data for the authenticated user, including compatibility information.

```http
GET /api/auth/user/subscription
```

**Response:**

```json
{
  "success": true,
  "message": "User subscription retrieved successfully",
  "data": {
    "credits": 1000,
    "queues": 5,
    "legacy": {
      "subscriptionStatus": "active",
      "subscriptionPlan": "pro",
      "datePaid": "2024-01-15T10:00:00Z",
      "paymentProcessorUserId": "cus_stripe123",
      "lemonSqueezyCustomerPortalUrl": null
    },
    "subscription": {
      "id": "660e8400-e29b-41d4-a716-446655440001",
      "subscriptionId": "550e8400-e29b-41d4-a716-446655440000",
      "status": "active",
      "startDate": "2024-01-15T10:00:00Z",
      "endDate": "2024-02-14T10:00:00Z",
      "creditsAllocated": 1000,
      "subscription": {
        "name": "Professional Plan",
        "description": "Advanced features for professionals",
        "price": 29.99,
        "creditsIncluded": 1000,
        "features": [
          "unlimited_credits",
          "priority_support",
          "api_access"
        ]
      }
    },
    "effective": {
      "subscriptionStatus": "active",
      "subscriptionPlan": "Professional Plan",
      "hasActiveSubscription": true,
      "subscriptionEndDate": "2024-02-14T10:00:00Z",
      "totalCreditsAllocated": 1000
    },
    "compatibility": {
      "hasNewSubscription": true,
      "hasLegacySubscription": true,
      "isFullyMigrated": true,
      "needsMigration": false
    }
  }
}
```

**Status Codes:**
- `200` - Success
- `401` - Unauthorized
- `404` - User not found
- `500` - Internal server error

---

### Get User Subscription (Legacy Format)

Retrieve subscription data in the legacy format for backward compatibility.

```http
GET /api/auth/user/subscription/legacy
```

**Response:**

```json
{
  "success": true,
  "message": "User subscription retrieved successfully",
  "data": {
    "subscriptionStatus": "active",
    "subscriptionPlan": "pro",
    "datePaid": "2024-01-15T10:00:00Z",
    "credits": 1000,
    "queues": 5,
    "paymentProcessorUserId": "cus_stripe123",
    "lemonSqueezyCustomerPortalUrl": null,
    "hasActiveSubscription": true,
    "subscriptionEndDate": "2024-02-14T10:00:00Z"
  }
}
```

**Status Codes:**
- `200` - Success
- `401` - Unauthorized
- `404` - User not found
- `500` - Internal server error

---

### Check Subscription Status

Simple endpoint to check if user has an active subscription.

```http
GET /api/auth/user/subscription/check
```

**Response:**

```json
{
  "success": true,
  "message": "Subscription status retrieved successfully",
  "data": {
    "hasActiveSubscription": true,
    "subscriptionStatus": "active",
    "subscriptionPlan": "pro"
  }
}
```

**Status Codes:**
- `200` - Success
- `401` - Unauthorized
- `500` - Internal server error

---

### Get User Credits

Retrieve user's current credit balance and allocation information.

```http
GET /api/auth/user/credits
```

**Response:**

```json
{
  "success": true,
  "message": "User credits retrieved successfully",
  "data": {
    "credits": 1000,
    "totalCreditsAllocated": 1000,
    "subscriptionPlan": "pro",
    "hasActiveSubscription": true
  }
}
```

**Status Codes:**
- `200` - Success
- `401` - Unauthorized
- `500` - Internal server error

---

## Checkout & Payments

### Generate Checkout Session

Create a payment checkout session for a subscription plan.

```http
POST /api/auth/checkout
```

**Request Body:**

```json
{
  "planId": "hobby"
}
```

**Valid Plan IDs:**
- `free` - Free plan
- `hobby` - Hobby plan ($9.99/month)
- `pro` - Professional plan ($29.99/month)
- `credits10` - Credit package ($9.99 one-time)

**Response:**

```json
{
  "success": true,
  "message": "Checkout session created successfully",
  "data": {
    "sessionUrl": "https://checkout.stripe.com/pay/cs_test_...",
    "sessionId": "cs_test_a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6"
  }
}
```

**Status Codes:**
- `200` - Checkout session created
- `400` - Invalid plan ID
- `401` - Unauthorized
- `403` - User needs email for payment
- `500` - Internal server error

---

## Feature Flags

### Get Feature Flags

Retrieve current feature flag settings.

```http
GET /api/auth/feature-flags
```

**Response:**

```json
{
  "success": true,
  "message": "Feature flags retrieved successfully",
  "data": {
    "useNewSubscriptionSystem": false,
    "enableDualSystemOperation": true,
    "preferNewSystemData": false,
    "enableLegacyFallback": true
  }
}
```

**Status Codes:**
- `200` - Success
- `500` - Internal server error

---

### Set Feature Flag

Update a feature flag setting (admin only).

```http
POST /api/auth/feature-flags
```

**Request Body:**

```json
{
  "flag": "enableDualSystemOperation",
  "value": true
}
```

**Available Flags:**
- `useNewSubscriptionSystem` - Use new system exclusively
- `enableDualSystemOperation` - Run both legacy and new systems
- `preferNewSystemData` - Prioritize new system data over legacy
- `enableLegacyFallback` - Fallback to legacy system on errors

**Response:**

```json
{
  "success": true,
  "message": "Feature flag enableDualSystemOperation set to true",
  "data": {
    "flag": "enableDualSystemOperation",
    "value": true
  }
}
```

**Status Codes:**
- `200` - Feature flag updated
- `400` - Invalid flag or value
- `403` - Admin access required
- `500` - Internal server error

---

## Webhooks

### Stripe Webhook

Endpoint for Stripe payment processor webhooks.

```http
POST /api/webhooks/stripe
```

**Headers:**
- `stripe-signature` - Stripe webhook signature (required)

**Supported Events:**
- `checkout.session.completed`
- `payment_intent.succeeded`
- `invoice.payment_succeeded`
- `customer.subscription.updated`
- `customer.subscription.deleted`

---

### LemonSqueezy Webhook

Endpoint for LemonSqueezy payment processor webhooks.

```http
POST /api/webhooks/lemonsqueezy
```

**Headers:**
- `x-signature` - LemonSqueezy webhook signature (required)

**Supported Events:**
- `order_created`
- `subscription_created`
- `subscription_updated`
- `subscription_cancelled`
- `subscription_expired`

---

## Error Responses

All endpoints return errors in the following format:

```json
{
  "success": false,
  "message": "Error description",
  "error": "Detailed error information"
}
```

### Common Error Codes

| Status | Description |
|--------|-------------|
| `400` | Bad Request - Invalid input data |
| `401` | Unauthorized - Missing or invalid authentication |
| `403` | Forbidden - Insufficient permissions |
| `404` | Not Found - Resource not found |
| `409` | Conflict - Resource already exists |
| `500` | Internal Server Error - Server error |

### Error Examples

**Invalid Plan ID:**
```json
{
  "success": false,
  "message": "Invalid subscription plan",
  "error": "Plan 'invalid-plan' not found"
}
```

**Unauthorized Access:**
```json
{
  "success": false,
  "message": "User not authenticated",
  "error": "Authorization header missing or invalid"
}
```

**Subscription Conflict:**
```json
{
  "success": false,
  "message": "User already has active subscription",
  "error": "Cannot create multiple active subscriptions"
}
```

---

## Rate Limiting

API endpoints are rate limited to prevent abuse:

- **Authenticated endpoints**: 100 requests per minute per user
- **Public endpoints**: 60 requests per minute per IP
- **Webhook endpoints**: 1000 requests per minute per IP

Rate limit headers are included in responses:

```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1640995200
```

---

## SDK Examples

### JavaScript/TypeScript

```typescript
class SubscriptionAPI {
  constructor(private baseUrl: string, private sessionToken: string) {}

  async getSubscriptions() {
    const response = await fetch(`${this.baseUrl}/api/subscriptions`);
    return response.json();
  }

  async getUserSubscription() {
    const response = await fetch(`${this.baseUrl}/api/auth/user/subscription`, {
      headers: { Authorization: `Bearer ${this.sessionToken}` }
    });
    return response.json();
  }

  async createCheckoutSession(planId: string) {
    const response = await fetch(`${this.baseUrl}/api/auth/checkout`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${this.sessionToken}`
      },
      body: JSON.stringify({ planId })
    });
    return response.json();
  }
}
```

### cURL Examples

```bash
# Get subscription plans
curl -X GET "https://api.example.com/api/subscriptions"

# Get user subscription
curl -X GET "https://api.example.com/api/auth/user/subscription" \
  -H "Authorization: Bearer <session_token>"

# Create checkout session
curl -X POST "https://api.example.com/api/auth/checkout" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <session_token>" \
  -d '{"planId": "hobby"}'
```

# Dalti Subscription API - Quick Reference

## 🚀 Base URLs
- **Production**: `https://api.dalti.app`
- **Staging**: `https://api-staging.dalti.app`

## 🔐 Authentication
```javascript
// API Key (Server-to-Server)
headers: { 'X-API-Key': 'your-api-key' }

// JWT <PERSON>ken (User Operations)  
headers: { 'Authorization': 'Bearer jwt-token' }
```

## 📋 Essential Endpoints

### Get Subscription Plans
```http
GET /api/external/subscriptions
X-API-Key: your-api-key
```

### Create Checkout Session
```http
POST /api/external/checkout
X-API-Key: your-api-key
Content-Type: application/json

{
  "planIdentifier": "pro-plan",
  "returnUrl": "https://yoursite.com/success",
  "cancelUrl": "https://yoursite.com/cancel",
  "customerInfo": {
    "email": "<EMAIL>",
    "name": "<PERSON>"
  }
}
```

### Check Checkout Status
```http
GET /api/external/checkout/status/{sessionId}
X-API-Key: your-api-key
```

### Generate Customer Portal
```http
POST /api/portal/sessions
X-API-Key: your-api-key
Content-Type: application/json

{
  "email": "<EMAIL>",
  "returnUrl": "https://yoursite.com/dashboard",
  "features": ["subscription_management", "billing_history"],
  "theme": {
    "primaryColor": "#007bff",
    "logoUrl": "https://yoursite.com/logo.png"
  }
}
```

### Register Webhook
```http
POST /api/webhooks/callbacks
X-API-Key: your-api-key
Content-Type: application/json

{
  "url": "https://yoursite.com/webhooks/dalti",
  "secret": "your-webhook-secret",
  "events": ["subscription.created", "payment.succeeded"]
}
```

## 🔗 Webhook Events
- `subscription.created` - New subscription activated
- `subscription.updated` - Subscription modified
- `subscription.cancelled` - Subscription cancelled
- `payment.succeeded` - Payment completed
- `payment.failed` - Payment failed

## 🎨 Quick Integration

### 1. Load Plans
```javascript
const response = await fetch('https://api.dalti.app/api/external/subscriptions', {
  headers: { 'X-API-Key': 'your-api-key' }
});
const { data } = await response.json();
console.log(data.subscriptions);
```

### 2. Create Checkout
```javascript
const checkout = await fetch('https://api.dalti.app/api/external/checkout', {
  method: 'POST',
  headers: {
    'X-API-Key': 'your-api-key',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    planIdentifier: 'pro-plan',
    returnUrl: 'https://yoursite.com/success',
    cancelUrl: 'https://yoursite.com/cancel',
    customerInfo: { email: '<EMAIL>' }
  })
});
const { data } = await checkout.json();
window.location.href = data.sessionUrl;
```

### 3. Handle Webhooks (Node.js)
```javascript
app.post('/webhooks/dalti', express.raw({type: 'application/json'}), (req, res) => {
  const signature = req.headers['x-webhook-signature'];
  const payload = req.body;
  
  // Verify signature
  const expectedSignature = crypto
    .createHmac('sha256', process.env.WEBHOOK_SECRET)
    .update(payload)
    .digest('hex');
    
  if (signature !== expectedSignature) {
    return res.status(401).send('Invalid signature');
  }
  
  const event = JSON.parse(payload);
  console.log('Webhook:', event.eventType);
  
  res.status(200).send('OK');
});
```

## ❌ Error Handling
```javascript
try {
  const response = await fetch(endpoint, options);
  const data = await response.json();
  
  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${data.message}`);
  }
  
  if (!data.success) {
    throw new Error(data.message);
  }
  
  return data.data;
} catch (error) {
  console.error('API Error:', error.message);
}
```

## 📊 Response Format
```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": {
    // Response data here
  }
}
```

## 🚨 HTTP Status Codes
- `200` - Success
- `201` - Created  
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `429` - Rate Limited
- `500` - Server Error

## 🎯 Testing
- **Staging URL**: `https://api-staging.dalti.app`
- **Test API Key**: Contact support
- **Webhook Testing**: Use ngrok for local development

## 📞 Support
- **Email**: <EMAIL>
- **Docs**: https://docs.dalti.app
- **Status**: https://status.dalti.app

## 🔄 Rate Limits
- **1000 requests/hour** per API key
- **10 portal sessions/hour** per user
- **5 webhook retries** with exponential backoff

---

**Need help?** Check the [full integration guide](./EXTERNAL_INTEGRATION_GUIDE.md) or contact support!

# Dalti Subscription System Integration Guide

## 📋 Table of Contents

1. [Overview](#overview)
2. [Getting Started](#getting-started)
3. [Authentication](#authentication)
4. [Quick Start Examples](#quick-start-examples)
5. [API Reference](#api-reference)
6. [Integration Patterns](#integration-patterns)
7. [Webhooks](#webhooks)
8. [Customer Portal](#customer-portal)
9. [<PERSON><PERSON><PERSON>ling](#error-handling)
10. [Testing](#testing)
11. [Support](#support)

## 🌟 Overview

The Dalti Subscription System provides a comprehensive API for integrating subscription management into your website. This system supports:

- **Multiple subscription plans** with flexible pricing
- **Secure checkout sessions** with return URL handling
- **Real-time webhooks** for payment events
- **Customer portal integration** for self-service management
- **Comprehensive analytics** and reporting
- **Dual authentication** (JWT tokens and API keys)

### Base URLs

| Environment | URL |
|-------------|-----|
| Production | `https://api.dalti.app` |
| Staging | `https://api-staging.dalti.app` |
| Development | `http://localhost:5400` |

## 🚀 Getting Started

### 1. Obtain API Credentials

Contact our team to get your API credentials:
- **API Key** for server-to-server operations
- **Webhook Secret** for signature verification

### 2. Test Connection

```bash
curl -X GET "https://api.dalti.app/api/external/subscriptions" \
  -H "X-API-Key: your-api-key-here"
```

### 3. Implement Basic Integration

Follow our [Quick Start Examples](#quick-start-examples) to implement basic subscription functionality.

## 🔐 Authentication

### API Key Authentication (Recommended for Server-to-Server)

Include your API key in the request header:

```javascript
const headers = {
  'X-API-Key': 'your-api-key-here',
  'Content-Type': 'application/json'
};
```

### JWT Authentication (For User-Specific Operations)

Include the JWT token in the Authorization header:

```javascript
const headers = {
  'Authorization': 'Bearer your-jwt-token',
  'Content-Type': 'application/json'
};
```

## ⚡ Quick Start Examples

### 1. Display Available Subscription Plans

```javascript
async function loadSubscriptionPlans() {
  try {
    const response = await fetch('https://api.dalti.app/api/external/subscriptions', {
      headers: {
        'X-API-Key': 'your-api-key-here'
      }
    });
    
    const data = await response.json();
    
    if (data.success) {
      displayPlans(data.data.subscriptions);
    }
  } catch (error) {
    console.error('Failed to load plans:', error);
  }
}

function displayPlans(plans) {
  const container = document.getElementById('subscription-plans');
  
  container.innerHTML = plans.map(plan => `
    <div class="plan-card" data-plan-id="${plan.id}">
      <h3>${plan.name}</h3>
      <p>${plan.description}</p>
      <div class="price">$${(plan.price / 100).toFixed(2)}/${plan.interval}</div>
      <div class="features">
        <p>✅ ${plan.creditsIncluded} credits included</p>
        ${plan.features.map(feature => `<p>✅ ${feature}</p>`).join('')}
      </div>
      <button onclick="selectPlan('${plan.id}')">Choose Plan</button>
    </div>
  `).join('');
}
```

### 2. Create Checkout Session

```javascript
async function selectPlan(planId) {
  const checkoutData = {
    planIdentifier: planId,
    returnUrl: window.location.origin + '/success',
    cancelUrl: window.location.origin + '/cancel',
    customerInfo: {
      email: getCurrentUserEmail(),
      name: getCurrentUserName()
    }
  };

  try {
    const response = await fetch('https://api.dalti.app/api/external/checkout', {
      method: 'POST',
      headers: {
        'X-API-Key': 'your-api-key-here',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(checkoutData)
    });

    const checkout = await response.json();
    
    if (checkout.success) {
      // Redirect to checkout
      window.location.href = checkout.data.sessionUrl;
    } else {
      showError('Failed to create checkout session');
    }
  } catch (error) {
    console.error('Checkout failed:', error);
    showError('An error occurred during checkout');
  }
}
```

### 3. Handle Checkout Success

```javascript
// On your success page
async function handleCheckoutSuccess() {
  const urlParams = new URLSearchParams(window.location.search);
  const sessionId = urlParams.get('session_id');
  
  if (sessionId) {
    try {
      const response = await fetch(`https://api.dalti.app/api/external/checkout/status/${sessionId}`, {
        headers: {
          'X-API-Key': 'your-api-key-here'
        }
      });
      
      const data = await response.json();
      
      if (data.success && data.data.status === 'completed') {
        showSuccessMessage('Subscription activated successfully!');
        updateUserInterface(data.data.subscription);
      }
    } catch (error) {
      console.error('Failed to verify checkout:', error);
    }
  }
}
```

## 📚 API Reference

### Subscription Management

#### Get Available Subscriptions
```http
GET /api/external/subscriptions
```

**Response:**
```json
{
  "success": true,
  "message": "Subscriptions retrieved successfully",
  "data": {
    "subscriptions": [
      {
        "id": "sub_123",
        "name": "Pro Plan",
        "description": "Perfect for growing businesses",
        "price": 2000,
        "priceFormatted": "$20.00",
        "interval": "monthly",
        "creditsIncluded": 1000,
        "features": ["Advanced analytics", "Priority support"],
        "isActive": true
      }
    ],
    "total": 3
  }
}
```

#### Create Checkout Session
```http
POST /api/external/checkout
```

**Request Body:**
```json
{
  "planIdentifier": "pro-plan",
  "returnUrl": "https://yoursite.com/success",
  "cancelUrl": "https://yoursite.com/cancel",
  "customerInfo": {
    "email": "<EMAIL>",
    "name": "John Doe"
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "Checkout session created successfully",
  "data": {
    "sessionId": "cs_123456",
    "sessionUrl": "https://checkout.dalti.app/session/cs_123456",
    "expiresAt": "2025-07-22T10:00:00.000Z"
  }
}
```

### User Management

#### Get User Subscription Status
```http
GET /api/auth/users/subscription-status
Authorization: Bearer {jwt-token}
```

#### Get User Credits
```http
GET /api/users/credits?userId={user-id}
```

### Customer Portal

#### Generate Portal Session
```http
POST /api/portal/sessions
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "returnUrl": "https://yoursite.com/dashboard",
  "features": ["subscription_management", "billing_history", "payment_methods"],
  "theme": {
    "primaryColor": "#007bff",
    "logoUrl": "https://yoursite.com/logo.png"
  }
}
```

## 🎨 Integration Patterns

### Complete Subscription Widget

```html
<!DOCTYPE html>
<html>
<head>
    <title>Subscription Management</title>
    <style>
        .subscription-widget {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .plans-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .plan-card {
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            padding: 24px;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .plan-card:hover {
            border-color: #007bff;
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0,123,255,0.15);
        }
        
        .plan-card.popular {
            border-color: #007bff;
            position: relative;
        }
        
        .plan-card.popular::before {
            content: "Most Popular";
            position: absolute;
            top: -10px;
            left: 50%;
            transform: translateX(-50%);
            background: #007bff;
            color: white;
            padding: 4px 16px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .price {
            font-size: 2.5em;
            font-weight: bold;
            color: #007bff;
            margin: 16px 0;
        }
        
        .features {
            text-align: left;
            margin: 20px 0;
        }
        
        .features p {
            margin: 8px 0;
            padding-left: 20px;
            position: relative;
        }
        
        .features p::before {
            content: "✅";
            position: absolute;
            left: 0;
        }
        
        .select-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            width: 100%;
            transition: background 0.3s ease;
        }
        
        .select-button:hover {
            background: #0056b3;
        }
        
        .current-subscription {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 12px;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            padding: 12px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="subscription-widget">
        <h1>Choose Your Subscription Plan</h1>
        
        <div id="current-subscription" class="current-subscription" style="display: none;">
            <h3>Current Subscription</h3>
            <div id="current-plan-details"></div>
            <button onclick="openCustomerPortal()" class="select-button" style="width: auto; margin-top: 10px;">
                Manage Subscription
            </button>
        </div>
        
        <div id="loading" class="loading">
            <p>Loading subscription plans...</p>
        </div>
        
        <div id="error-message" class="error" style="display: none;"></div>
        <div id="success-message" class="success" style="display: none;"></div>
        
        <div id="plans-container" class="plans-grid"></div>
    </div>

    <script>
        class SubscriptionWidget {
            constructor() {
                this.apiKey = 'your-api-key-here'; // Replace with your actual API key
                this.baseUrl = 'https://api.dalti.app';
                this.currentUser = this.getCurrentUser();
                
                this.init();
            }
            
            async init() {
                try {
                    await this.loadCurrentSubscription();
                    await this.loadSubscriptionPlans();
                } catch (error) {
                    this.showError('Failed to initialize subscription widget');
                    console.error('Widget initialization failed:', error);
                }
            }
            
            async loadCurrentSubscription() {
                if (!this.currentUser?.email) return;
                
                try {
                    // Check if user has an active subscription
                    const response = await fetch(`${this.baseUrl}/api/users/subscription-status?email=${this.currentUser.email}`, {
                        headers: {
                            'X-API-Key': this.apiKey
                        }
                    });
                    
                    const data = await response.json();
                    
                    if (data.success && data.data.hasActiveSubscription) {
                        this.displayCurrentSubscription(data.data.subscription);
                    }
                } catch (error) {
                    console.error('Failed to load current subscription:', error);
                }
            }
            
            async loadSubscriptionPlans() {
                try {
                    const response = await fetch(`${this.baseUrl}/api/external/subscriptions`, {
                        headers: {
                            'X-API-Key': this.apiKey
                        }
                    });
                    
                    const data = await response.json();
                    
                    if (data.success) {
                        this.displayPlans(data.data.subscriptions);
                    } else {
                        throw new Error(data.message);
                    }
                } catch (error) {
                    this.showError('Failed to load subscription plans');
                    console.error('Failed to load plans:', error);
                } finally {
                    document.getElementById('loading').style.display = 'none';
                }
            }
            
            displayCurrentSubscription(subscription) {
                const container = document.getElementById('current-subscription');
                const details = document.getElementById('current-plan-details');
                
                details.innerHTML = `
                    <p><strong>Plan:</strong> ${subscription.name}</p>
                    <p><strong>Status:</strong> ${subscription.status}</p>
                    <p><strong>Credits:</strong> ${subscription.creditsRemaining} remaining</p>
                    <p><strong>Next billing:</strong> ${new Date(subscription.nextBilling).toLocaleDateString()}</p>
                `;
                
                container.style.display = 'block';
            }
            
            displayPlans(plans) {
                const container = document.getElementById('plans-container');
                
                container.innerHTML = plans.map((plan, index) => `
                    <div class="plan-card ${index === 1 ? 'popular' : ''}" data-plan-id="${plan.id}">
                        <h3>${plan.name}</h3>
                        <p>${plan.description}</p>
                        <div class="price">$${(plan.price / 100).toFixed(2)}<span style="font-size: 0.4em;">/${plan.interval}</span></div>
                        <div class="features">
                            <p>${plan.creditsIncluded} credits included</p>
                            ${plan.features.map(feature => `<p>${feature}</p>`).join('')}
                        </div>
                        <button class="select-button" onclick="widget.selectPlan('${plan.id}', '${plan.name}')">
                            Choose ${plan.name}
                        </button>
                    </div>
                `).join('');
            }
            
            async selectPlan(planId, planName) {
                if (!this.currentUser?.email) {
                    this.showError('Please log in to subscribe to a plan');
                    return;
                }
                
                const checkoutData = {
                    planIdentifier: planId,
                    returnUrl: window.location.origin + '/success?plan=' + encodeURIComponent(planName),
                    cancelUrl: window.location.href,
                    customerInfo: {
                        email: this.currentUser.email,
                        name: this.currentUser.name || this.currentUser.email
                    }
                };
                
                try {
                    const response = await fetch(`${this.baseUrl}/api/external/checkout`, {
                        method: 'POST',
                        headers: {
                            'X-API-Key': this.apiKey,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(checkoutData)
                    });
                    
                    const checkout = await response.json();
                    
                    if (checkout.success) {
                        // Redirect to checkout
                        window.location.href = checkout.data.sessionUrl;
                    } else {
                        this.showError('Failed to create checkout session: ' + checkout.message);
                    }
                } catch (error) {
                    this.showError('An error occurred during checkout');
                    console.error('Checkout failed:', error);
                }
            }
            
            async openCustomerPortal() {
                if (!this.currentUser?.email) {
                    this.showError('Please log in to access the customer portal');
                    return;
                }
                
                const portalData = {
                    email: this.currentUser.email,
                    returnUrl: window.location.href,
                    features: ['subscription_management', 'billing_history', 'payment_methods'],
                    theme: {
                        primaryColor: '#007bff',
                        logoUrl: window.location.origin + '/logo.png'
                    }
                };
                
                try {
                    const response = await fetch(`${this.baseUrl}/api/portal/sessions`, {
                        method: 'POST',
                        headers: {
                            'X-API-Key': this.apiKey,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(portalData)
                    });
                    
                    const portal = await response.json();
                    
                    if (portal.success) {
                        window.open(portal.data.portalUrl, '_blank');
                    } else {
                        this.showError('Failed to open customer portal: ' + portal.message);
                    }
                } catch (error) {
                    this.showError('An error occurred while opening the customer portal');
                    console.error('Portal failed:', error);
                }
            }
            
            getCurrentUser() {
                // Replace this with your actual user detection logic
                // This could come from your authentication system, localStorage, etc.
                return {
                    email: '<EMAIL>', // Replace with actual user email
                    name: 'John Doe' // Replace with actual user name
                };
            }
            
            showError(message) {
                const errorDiv = document.getElementById('error-message');
                errorDiv.textContent = message;
                errorDiv.style.display = 'block';
                
                setTimeout(() => {
                    errorDiv.style.display = 'none';
                }, 5000);
            }
            
            showSuccess(message) {
                const successDiv = document.getElementById('success-message');
                successDiv.textContent = message;
                successDiv.style.display = 'block';
                
                setTimeout(() => {
                    successDiv.style.display = 'none';
                }, 5000);
            }
        }
        
        // Initialize the widget when the page loads
        let widget;
        document.addEventListener('DOMContentLoaded', function() {
            widget = new SubscriptionWidget();
        });
        
        // Handle checkout success (if on success page)
        if (window.location.pathname.includes('/success')) {
            document.addEventListener('DOMContentLoaded', function() {
                const urlParams = new URLSearchParams(window.location.search);
                const planName = urlParams.get('plan');
                
                if (planName) {
                    document.body.innerHTML = `
                        <div style="text-align: center; padding: 50px;">
                            <h1>🎉 Subscription Activated!</h1>
                            <p>Thank you for subscribing to ${decodeURIComponent(planName)}.</p>
                            <p>Your subscription is now active and you can start using all the features.</p>
                            <a href="/" style="display: inline-block; background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin-top: 20px;">
                                Return to Dashboard
                            </a>
                        </div>
                    `;
                }
            });
        }
    </script>
</body>
</html>
```

This widget provides:
- **Responsive design** that works on all devices
- **Current subscription display** for existing customers
- **Plan comparison** with highlighted popular option
- **Integrated checkout flow** with error handling
- **Customer portal access** for subscription management
- **Success page handling** with confirmation message

### Usage Instructions:

1. **Replace the API key** in the JavaScript with your actual API key
2. **Update the user detection logic** in `getCurrentUser()` to match your authentication system
3. **Customize the styling** to match your website's design
4. **Add your logo URL** in the customer portal configuration
5. **Test the integration** in your development environment first

This complete widget can be embedded into any website and provides a full subscription management experience for your users.

## 🔗 Webhooks

Webhooks allow you to receive real-time notifications about subscription events.

### Setting Up Webhooks

#### 1. Register Your Webhook Endpoint

```javascript
const webhookConfig = {
  url: 'https://yoursite.com/webhooks/dalti',
  secret: 'your-webhook-secret',
  events: [
    'subscription.created',
    'subscription.updated',
    'subscription.cancelled',
    'payment.succeeded',
    'payment.failed'
  ]
};

const response = await fetch('https://api.dalti.app/api/webhooks/callbacks', {
  method: 'POST',
  headers: {
    'X-API-Key': 'your-api-key-here',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(webhookConfig)
});
```

#### 2. Handle Webhook Events (Node.js/Express)

```javascript
const express = require('express');
const crypto = require('crypto');
const app = express();

// Middleware to capture raw body for signature verification
app.use('/webhooks/dalti', express.raw({type: 'application/json'}));

app.post('/webhooks/dalti', (req, res) => {
  const signature = req.headers['x-webhook-signature'];
  const payload = req.body;
  const secret = process.env.DALTI_WEBHOOK_SECRET;

  // Verify webhook signature
  if (!verifyWebhookSignature(payload, signature, secret)) {
    console.error('Invalid webhook signature');
    return res.status(401).send('Unauthorized');
  }

  try {
    const event = JSON.parse(payload);
    console.log('Received webhook:', event.eventType);

    // Handle different event types
    switch (event.eventType) {
      case 'subscription.created':
        handleSubscriptionCreated(event.data);
        break;
      case 'subscription.cancelled':
        handleSubscriptionCancelled(event.data);
        break;
      case 'payment.succeeded':
        handlePaymentSucceeded(event.data);
        break;
      case 'payment.failed':
        handlePaymentFailed(event.data);
        break;
      default:
        console.log('Unhandled event type:', event.eventType);
    }

    res.status(200).send('OK');
  } catch (error) {
    console.error('Webhook processing error:', error);
    res.status(500).send('Internal Server Error');
  }
});

function verifyWebhookSignature(payload, signature, secret) {
  const expectedSignature = crypto
    .createHmac('sha256', secret)
    .update(payload)
    .digest('hex');

  return crypto.timingSafeEqual(
    Buffer.from(signature, 'hex'),
    Buffer.from(expectedSignature, 'hex')
  );
}

function handleSubscriptionCreated(data) {
  console.log('New subscription created:', data);
  // Update your database
  // Send welcome email
  // Grant access to premium features
}

function handleSubscriptionCancelled(data) {
  console.log('Subscription cancelled:', data);
  // Update user access
  // Send cancellation confirmation
  // Schedule data retention cleanup
}

function handlePaymentSucceeded(data) {
  console.log('Payment succeeded:', data);
  // Update subscription status
  // Send payment receipt
  // Extend service period
}

function handlePaymentFailed(data) {
  console.log('Payment failed:', data);
  // Notify user of failed payment
  // Implement retry logic
  // Update subscription status
}
```

### Webhook Event Types

| Event Type | Description | When Triggered |
|------------|-------------|----------------|
| `subscription.created` | New subscription activated | After successful payment |
| `subscription.updated` | Subscription modified | Plan change, billing update |
| `subscription.cancelled` | Subscription cancelled | User or admin cancellation |
| `subscription.expired` | Subscription expired | End of billing period |
| `payment.succeeded` | Payment completed | Successful charge |
| `payment.failed` | Payment failed | Failed charge attempt |
| `user.created` | New user registered | Account creation |
| `credits.updated` | User credits changed | Credit addition/deduction |

## 🎨 Customer Portal

The customer portal allows users to manage their subscriptions independently.

### Opening the Customer Portal

```javascript
async function openCustomerPortal(userEmail) {
  const portalData = {
    email: userEmail,
    returnUrl: window.location.href,
    features: [
      'subscription_management',
      'billing_history',
      'payment_methods',
      'usage_analytics'
    ],
    theme: {
      primaryColor: '#007bff',
      backgroundColor: '#ffffff',
      fontFamily: 'Inter, sans-serif',
      logoUrl: 'https://yoursite.com/logo.png'
    },
    sessionDuration: 3600 // 1 hour
  };

  try {
    const response = await fetch('https://api.dalti.app/api/portal/sessions', {
      method: 'POST',
      headers: {
        'X-API-Key': 'your-api-key-here',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(portalData)
    });

    const portal = await response.json();

    if (portal.success) {
      // Open in new tab
      window.open(portal.data.portalUrl, '_blank');

      // Or redirect current page
      // window.location.href = portal.data.portalUrl;
    }
  } catch (error) {
    console.error('Failed to open customer portal:', error);
  }
}
```

### Portal Features

- **Subscription Management**: View, upgrade, downgrade, cancel
- **Billing History**: Download invoices, view payment history
- **Payment Methods**: Add, remove, update payment methods
- **Usage Analytics**: View credit usage and statistics
- **Account Settings**: Update profile information

## ❌ Error Handling

### Standard Error Response Format

All API endpoints return errors in a consistent format:

```json
{
  "success": false,
  "message": "Human-readable error message",
  "error": "Technical error details",
  "errors": {
    "field": ["Validation error message"]
  }
}
```

### HTTP Status Codes

| Code | Meaning | Description |
|------|---------|-------------|
| 200 | OK | Request successful |
| 201 | Created | Resource created successfully |
| 400 | Bad Request | Invalid request data |
| 401 | Unauthorized | Authentication required |
| 403 | Forbidden | Insufficient permissions |
| 404 | Not Found | Resource not found |
| 429 | Too Many Requests | Rate limit exceeded |
| 500 | Internal Server Error | Server error |

### Error Handling Best Practices

```javascript
async function apiCall(endpoint, options = {}) {
  try {
    const response = await fetch(endpoint, {
      headers: {
        'X-API-Key': 'your-api-key-here',
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    });

    const data = await response.json();

    // Check HTTP status
    if (!response.ok) {
      throw new APIError(`HTTP ${response.status}: ${data.message}`, response.status, data);
    }

    // Check API success flag
    if (!data.success) {
      throw new APIError(data.message || 'API request failed', response.status, data);
    }

    return data.data;
  } catch (error) {
    if (error instanceof APIError) {
      throw error;
    }

    // Network or parsing error
    throw new APIError('Network error: ' + error.message, 0, null);
  }
}

class APIError extends Error {
  constructor(message, status, data) {
    super(message);
    this.name = 'APIError';
    this.status = status;
    this.data = data;
  }
}

// Usage with error handling
try {
  const subscriptions = await apiCall('https://api.dalti.app/api/external/subscriptions');
  displaySubscriptions(subscriptions.subscriptions);
} catch (error) {
  if (error instanceof APIError) {
    switch (error.status) {
      case 401:
        showError('Please check your API key');
        break;
      case 429:
        showError('Too many requests. Please try again later.');
        break;
      case 500:
        showError('Server error. Please try again later.');
        break;
      default:
        showError('Error: ' + error.message);
    }
  } else {
    showError('Network error. Please check your connection.');
  }
}
```

## 🧪 Testing

### Test Environment

Use our staging environment for testing:
- **Base URL**: `https://api-staging.dalti.app`
- **Test API Key**: Contact support for test credentials
- **Test Cards**: Use standard test card numbers

### Testing Checklist

#### ✅ Basic Integration
- [ ] Load subscription plans successfully
- [ ] Create checkout session
- [ ] Handle checkout success/cancel
- [ ] Display error messages appropriately

#### ✅ Webhook Integration
- [ ] Receive webhook events
- [ ] Verify webhook signatures
- [ ] Handle all event types
- [ ] Implement retry logic for failures

#### ✅ Customer Portal
- [ ] Generate portal sessions
- [ ] Open portal in new tab/window
- [ ] Handle portal return URLs
- [ ] Test theme customization

#### ✅ Error Scenarios
- [ ] Invalid API key
- [ ] Network timeouts
- [ ] Rate limiting
- [ ] Invalid request data

### Sample Test Suite (Jest)

```javascript
const { apiCall, APIError } = require('./dalti-api');

describe('Dalti API Integration', () => {
  const testApiKey = process.env.DALTI_TEST_API_KEY;
  const baseUrl = 'https://api-staging.dalti.app';

  test('should load subscription plans', async () => {
    const data = await apiCall(`${baseUrl}/api/external/subscriptions`, {
      headers: { 'X-API-Key': testApiKey }
    });

    expect(data.subscriptions).toBeDefined();
    expect(Array.isArray(data.subscriptions)).toBe(true);
    expect(data.subscriptions.length).toBeGreaterThan(0);
  });

  test('should create checkout session', async () => {
    const checkoutData = {
      planIdentifier: 'test-plan',
      returnUrl: 'https://example.com/success',
      cancelUrl: 'https://example.com/cancel',
      customerInfo: {
        email: '<EMAIL>',
        name: 'Test User'
      }
    };

    const data = await apiCall(`${baseUrl}/api/external/checkout`, {
      method: 'POST',
      headers: { 'X-API-Key': testApiKey },
      body: JSON.stringify(checkoutData)
    });

    expect(data.sessionId).toBeDefined();
    expect(data.sessionUrl).toBeDefined();
    expect(data.expiresAt).toBeDefined();
  });

  test('should handle invalid API key', async () => {
    await expect(
      apiCall(`${baseUrl}/api/external/subscriptions`, {
        headers: { 'X-API-Key': 'invalid-key' }
      })
    ).rejects.toThrow(APIError);
  });
});
```

## 📞 Support

### Documentation
- **API Reference**: [https://docs.dalti.app/api](https://docs.dalti.app/api)
- **OpenAPI Spec**: [https://api.dalti.app/api/docs/openapi](https://api.dalti.app/api/docs/openapi)

### Contact
- **Email**: <EMAIL>
- **Discord**: [Join our developer community](https://discord.gg/dalti-dev)
- **GitHub**: [Report issues](https://github.com/dalti/api-issues)

### Rate Limits
- **API Calls**: 1000 requests per hour per API key
- **Webhook Retries**: 5 attempts with exponential backoff
- **Portal Sessions**: 10 sessions per hour per user

### SLA
- **Uptime**: 99.9% guaranteed
- **Response Time**: < 200ms average
- **Support Response**: < 24 hours

---

## 🚀 Ready to Get Started?

1. **Contact us** for API credentials
2. **Test the integration** in staging environment
3. **Deploy to production** with confidence
4. **Monitor usage** through our analytics dashboard

**Happy coding! 🎉**

# Payment System Integration PRD

## Project Overview
Integrate the newly implemented subscription management system with the existing payment processor infrastructure (LemonSqueezy/Stripe) to create a unified, API-first subscription platform that supports external website integration.

## Current State Analysis

### Existing Payment System
- **Payment Processors**: Stripe and LemonSqueezy (currently using LemonSqueezy)
- **Payment Plans**: Defined via PaymentPlanId enum (Free, Hobby, Pro, Credits10)
- **User Subscription Tracking**: Direct User model fields (subscriptionStatus, subscriptionPlan, credits, queues)
- **Checkout Flow**: generateCheckoutSession operation using PaymentPlanId
- **Webhook Handling**: Direct User model updates via updateUserLemonSqueezyPaymentDetails

### New Subscription System
- **Subscription Model**: Plan definitions with comprehensive metadata
- **UserSubscription Model**: Individual subscription tracking with status management
- **API Endpoints**: Basic CRUD operations for subscription management
- **Validation**: Zod schemas for request validation

## Integration Requirements

### 1. Database Schema Enhancement
- Add paymentProcessorPlanId field to Subscription model
- Ensure proper indexing for performance optimization
- Maintain backward compatibility with existing User model fields

### 2. Payment Processor Integration
- Create mapping layer between Subscription model and payment processor plans
- Update checkout flow to support both legacy PaymentPlanId and new Subscription IDs
- Enhance webhook handlers to create UserSubscription records while maintaining User model updates
- Implement dual-system operation during transition period

### 3. API Development for External Website
- **Checkout APIs**: Create subscription-based checkout sessions with return URL handling
- **Subscription Management**: Complete CRUD operations for subscription lifecycle
- **User Management**: Credit balance, subscription status, and history APIs
- **Webhook Endpoints**: Enhanced payment processor event handling
- **Authentication**: Support for both JWT tokens and API key authentication

### 4. Data Migration Strategy
- Migrate existing user subscription data to UserSubscription model
- Create Subscription records matching existing PaymentPlanId plans
- Implement gradual transition from legacy to new system
- Ensure data integrity throughout migration process

### 5. External Website Integration Support
- **Server-to-Server APIs**: API key-based authentication for backend operations
- **User-Specific APIs**: JWT token-based authentication for user operations
- **Payment Flow APIs**: Complete checkout session management with callbacks
- **Customer Portal**: Subscription management interface integration

## Success Criteria
1. **Backward Compatibility**: Existing payment flows continue to work without interruption
2. **API Coverage**: Complete API support for external website subscription management
3. **Data Integrity**: All existing subscription data successfully migrated to new system
4. **Payment Processor Integration**: Seamless operation with LemonSqueezy/Stripe webhooks
5. **Performance**: No degradation in checkout or subscription management performance
6. **Documentation**: Comprehensive API documentation for external website developers

## Technical Constraints
- Must maintain existing User model subscription fields for backward compatibility
- Payment processor configurations (LemonSqueezy/Stripe) must remain unchanged
- Existing webhook endpoints must continue to function
- No breaking changes to current payment operations
- Support for both subscription and one-time payment models

## Implementation Phases
1. **Phase 1**: Database schema updates and model enhancements
2. **Phase 2**: Payment processor integration and mapping layer
3. **Phase 3**: API development and webhook enhancement
4. **Phase 4**: Data migration and system transition
5. **Phase 5**: Testing, documentation, and external website integration support

## External Dependencies
- LemonSqueezy/Stripe payment processor APIs
- Existing Wasp authentication system
- Current database schema and migration system
- External website development team requirements
